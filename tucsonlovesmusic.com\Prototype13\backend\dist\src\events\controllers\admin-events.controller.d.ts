import { Repository, DataSource } from 'typeorm';
import { EventsService } from '../events.service';
import { Event } from '../events.entity';
import { DatabaseResetService } from '../../database/reset.service';
import { MigrationSummary } from '../../database/migration-modules';
interface EventDiagnosticsResponse {
    totalEvents: number;
    eventsWithImages: number;
    eventsWithoutImages: number;
    events: Array<{
        id: string;
        name: string;
        imageUrl: string | null;
    }>;
}
interface EventImageFixResponse {
    total: number;
    processed: number;
    success: number;
    skipped: number;
    errors: number;
    message?: string;
}
export declare class AdminEventsController {
    private readonly eventsService;
    private readonly eventRepository;
    private readonly dataSource;
    private readonly databaseResetService;
    private readonly logger;
    constructor(eventsService: EventsService, eventRepository: Repository<Event>, dataSource: DataSource, databaseResetService: DatabaseResetService);
    private createAzureConnection;
    checkEventImages(): Promise<EventDiagnosticsResponse>;
    fixEventImages(): Promise<EventImageFixResponse & {
        message?: string;
    }>;
    resetEventsTable(): Promise<{
        success: boolean;
        message: string;
    }>;
    triggerFullMigration(): Promise<MigrationSummary>;
    triggerLimitedMigration(body: {
        limit?: number;
        prioritizeUpcoming?: boolean;
    }): Promise<MigrationSummary>;
    getMigrationStatus(): Promise<{
        lastMigration: Date | null;
        totalEvents: number;
        migrationModulesVersion: string;
    }>;
}
export {};
