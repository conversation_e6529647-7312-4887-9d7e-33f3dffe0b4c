"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/admin/event-relations/talent.tsx":
/*!*****************************************************!*\
  !*** ./components/admin/event-relations/talent.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventTalentRelations: () => (/* binding */ EventTalentRelations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-auth */ \"(app-pages-browser)/./hooks/use-auth.ts\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ EventTalentRelations auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction EventTalentRelations() {\n    _s();\n    const [counts, setCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [missingEvents, setMissingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mismatchedEvents, setMismatchedEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isBulkFixing, setIsBulkFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [batchFixing, setBatchFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [csvFixing, setCsvFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Add this line\n    const [batchFixProgress, setBatchFixProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 0,\n        total: 0,\n        success: 0,\n        failed: 0\n    });\n    const [fixingEvents, setFixingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // Track which events are being fixed\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        missing: false,\n        mismatched: false\n    });\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { getAccessToken } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // Handle CSV-based fix for all event-talent relationships\n    const handleCsvFix = async ()=>{\n        try {\n            setCsvFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            console.log('🔧 [Frontend] Starting CSV-based fix for event-talent relationships');\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/fix-from-csv\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('🔧 [Frontend] Server error response:', errorText);\n                throw new Error(\"Failed to execute CSV fix: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('🔧 [Frontend] CSV fix completed successfully:', result);\n            toast({\n                title: 'CSV Fix Complete',\n                description: result.message || 'Successfully processed event-talent relationships from CSV.',\n                variant: 'default'\n            });\n            // Refresh data to show updated counts\n            fetchData();\n        } catch (err) {\n            console.error('🔧 [Frontend] Error during CSV fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete CSV fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setCsvFixing(false);\n        }\n    };\n    // Fetch data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventTalentRelations.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"EventTalentRelations.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            setIsLoading(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Only fetch basic counts - avoid expensive missing/mismatched queries\n            const countsResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations?year=2025\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!countsResponse.ok) {\n                throw new Error('Failed to fetch event-talent relationship counts');\n            }\n            const countsData = await countsResponse.json();\n            setCounts(countsData);\n            // Skip the expensive missing/mismatched queries on initial load\n            // These will only be loaded when the user expands the sections\n            setMissingEvents([]);\n            setMismatchedEvents([]);\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching event-talent relationship data', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch event-talent relationship data');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Load expensive data only when sections are expanded\n    const loadSectionData = async (section)=>{\n        try {\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            if (section === 'missing' && missingEvents.length === 0) {\n                console.log('Loading missing events data...');\n                const missingResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/missing?year=2025\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token)\n                    }\n                });\n                if (missingResponse.ok) {\n                    const missingData = await missingResponse.json();\n                    setMissingEvents(missingData);\n                }\n            }\n            if (section === 'mismatched' && mismatchedEvents.length === 0) {\n                console.log('Loading mismatched events data...');\n                const mismatchedResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/mismatched?year=2025\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token)\n                    }\n                });\n                if (mismatchedResponse.ok) {\n                    const mismatchedData = await mismatchedResponse.json();\n                    setMismatchedEvents(mismatchedData);\n                }\n            }\n        } catch (err) {\n            console.error(\"Error loading \".concat(section, \" events data\"), err);\n            toast({\n                title: 'Error',\n                description: \"Failed to load \".concat(section, \" events data\"),\n                variant: 'destructive'\n            });\n        }\n    };\n    const toggleExpanded = async (section)=>{\n        const isCurrentlyExpanded = expanded[section];\n        // If expanding and no data loaded yet, load it first\n        if (!isCurrentlyExpanded) {\n            await loadSectionData(section);\n        }\n        setExpanded((prev)=>({\n                ...prev,\n                [section]: !prev[section]\n            }));\n    };\n    const handleFixAll = async ()=>{\n        try {\n            setIsBulkFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Pass year=2025 to match the same filter used in fetchData() for event display\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/fix-all-aggregated?year=2025\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fix all event-talent relationships');\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: result.message,\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing all event-talent relationships', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to fix all event-talent relationships',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsBulkFixing(false);\n        }\n    };\n    // Handle batch fixing using the bulk fix endpoint for better performance\n    const handleBatchFix = async ()=>{\n        try {\n            setBatchFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Check if there are events that need fixing\n            if (missingEvents.length === 0 && mismatchedEvents.length === 0) {\n                toast({\n                    title: 'No Events to Fix',\n                    description: 'There are no events that need fixing.',\n                    variant: 'default'\n                });\n                return;\n            }\n            console.log(\"\\uD83D\\uDD27 [Frontend] Starting bulk batch fix for all events\");\n            // Use the new aggregated batch fix endpoint which calls individual fixes for each event\n            // IMPORTANT: Pass the same year filter (2025) that's used for displaying events in fetchData()\n            // This ensures the batch fix processes the exact same events shown in the UI\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/fix-all-aggregated?year=2025\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to execute batch fix: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDD27 [Frontend] Batch fix completed successfully\");\n            toast({\n                title: 'Batch Fix Complete',\n                description: result.message || 'Successfully completed batch fix operation.',\n                variant: 'default'\n            });\n            // Refresh data to show updated counts\n            fetchData();\n        } catch (err) {\n            console.error('Error during batch fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete batch fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setBatchFixing(false);\n        }\n    };\n    // Open confirmation dialog for fixing an event\n    const openFixConfirmDialog = (event)=>{\n        setSelectedEvent(event);\n        setConfirmDialogOpen(true);\n    };\n    // Handle fixing a single event's talent relationships\n    const handleFixSingleEvent = async ()=>{\n        if (!selectedEvent) return;\n        try {\n            // Close the dialog\n            setConfirmDialogOpen(false);\n            // Mark this event as being fixed\n            setFixingEvents((prev)=>({\n                    ...prev,\n                    [selectedEvent.id]: true\n                }));\n            console.log(\"\\uD83D\\uDD27 [Frontend] Starting fix for event: \".concat(selectedEvent.name, \" (ID: \").concat(selectedEvent.id, \")\"));\n            console.log(\"\\uD83D\\uDD27 [Frontend] Event details:\", {\n                id: selectedEvent.id,\n                name: selectedEvent.name,\n                azure_talent_ids: selectedEvent.azure_talent_ids,\n                postgres_talent_count: selectedEvent.postgres_talent_count\n            });\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const url = \"\".concat(baseUrl, \"/admin/event-talent-relations/fix/\").concat(selectedEvent.id);\n            console.log(\"\\uD83D\\uDD27 [Frontend] Making POST request to: \".concat(url));\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            console.log(\"\\uD83D\\uDD27 [Frontend] Response status: \".concat(response.status, \" \").concat(response.statusText));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"\\uD83D\\uDD27 [Frontend] Server error response:\", errorText);\n                throw new Error(\"Failed to fix event-talent relationship for \".concat(selectedEvent.name, \": \").concat(response.status, \" \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDD27 [Frontend] Success response:\", result);\n            toast({\n                title: 'Success',\n                description: result.message || 'Fixed musician relationships for \"'.concat(selectedEvent.name, '\"'),\n                variant: 'default'\n            });\n            console.log(\"\\uD83D\\uDD27 [Frontend] Refreshing data after successful fix\");\n            // Refresh data after successful fix\n            fetchData();\n        } catch (err) {\n            console.error('🔧 [Frontend] Error fixing event-talent relationship', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : \"Failed to fix event-talent relationship for \".concat(selectedEvent.name),\n                variant: 'destructive'\n            });\n        } finally{\n            // Clear the fixing state for this event\n            if (selectedEvent) {\n                setFixingEvents((prev)=>({\n                        ...prev,\n                        [selectedEvent.id]: false\n                    }));\n            }\n            // Clear the selected event\n            setSelectedEvent(null);\n            console.log(\"\\uD83D\\uDD27 [Frontend] Fix operation completed\");\n        }\n    };\n    // Determine status message and icon color\n    const getStatusDetails = ()=>{\n        if (!counts) return {\n            message: 'Loading...',\n            status: 'neutral'\n        };\n        // Check if there are any actual problematic relationships in the dropdown lists\n        const hasProblematicRelations = missingEvents.length > 0 || mismatchedEvents.length > 0;\n        // If there are no problematic relations to troubleshoot, show green\n        // Only show yellow warning when there's something that needs attention\n        if (!hasProblematicRelations) {\n            return {\n                message: counts.missingTalents === 0 ? 'All events have talent relationships' : \"\".concat(counts.missingTalents, \" events with no musician relations\"),\n                status: 'good'\n            };\n        }\n        // Show yellow warning when there are items in the dropdown lists that need attention\n        return {\n            message: \"\".concat(counts.missingTalents, \" events (2025+) with no musician relations\"),\n            status: 'warning'\n        };\n    };\n    // Get the appropriate icon color based on status\n    const getIconColorClass = (status)=>{\n        switch(status){\n            case 'good':\n                return 'text-green-500';\n            case 'warning':\n                return 'text-yellow-500';\n            case 'bad':\n                return 'text-red-500';\n            default:\n                return 'text-muted-foreground';\n        }\n    };\n    const statusDetails = getStatusDetails();\n    // Format date for display\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'No date';\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"flex flex-row items-center space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-5 w-5 mr-2 \".concat(getIconColorClass(statusDetails.status))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-xl\",\n                            children: \"Events - Musicians\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                lineNumber: 453,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center justify-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-8 w-8 animate-spin text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-muted-foreground\",\n                            children: \"Loading relationship data...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 11\n                }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-8 px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-5 w-5 text-red-500 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-500\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: statusDetails.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleBatchFix,\n                                        disabled: batchFixing || missingEvents.length === 0,\n                                        className: \"flex items-center\",\n                                        variant: \"outline\",\n                                        children: batchFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Fixing \",\n                                                batchFixProgress.current,\n                                                \"/\",\n                                                batchFixProgress.total,\n                                                \"...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Batch Fix (10 Events)\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleCsvFix,\n                                        disabled: csvFixing,\n                                        className: \"flex items-center\",\n                                        variant: \"default\",\n                                        children: csvFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Processing CSV...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Fix from CSV (16,274 relationships)\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: fetchData,\n                                        disabled: isLoading || batchFixing || csvFixing,\n                                        title: \"Refresh data\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 15\n                            }, this),\n                            batchFixing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-2 bg-muted rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm mb-1 flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Batch Fix Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    batchFixProgress.current,\n                                                    \"/\",\n                                                    batchFixProgress.total,\n                                                    \" events\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-secondary h-2 rounded-full overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary h-full transition-all duration-300 ease-in-out\",\n                                            style: {\n                                                width: \"\".concat(batchFixProgress.current / Math.max(batchFixProgress.total, 1) * 100, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-xs mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-500\",\n                                                children: [\n                                                    batchFixProgress.success,\n                                                    \" successful\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: [\n                                                    batchFixProgress.failed,\n                                                    \" failed\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                        open: expanded.missing,\n                                        onOpenChange: ()=>toggleExpanded('missing'),\n                                        className: \"border rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Events Missing Musician Relationships (\",\n                                                                    missingEvents.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    expanded.missing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                className: \"px-4 pb-4\",\n                                                children: missingEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground py-2\",\n                                                    children: \"No events missing musician relationships\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Event Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 604,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Venue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Azure Talent IDs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"PostgreSQL Talents\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Action\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 608,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                            children: missingEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            className: \"font-medium\",\n                                                                            children: event.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 614,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: formatDate(event.date)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 615,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: event.venue_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 616,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: event.azure_talent_ids.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 617,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            className: \"text-red-500\",\n                                                                            children: event.postgres_talent_count\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 618,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>openFixConfirmDialog(event),\n                                                                                disabled: fixingEvents[event.id],\n                                                                                children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                            className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                                            lineNumber: 628,\n                                                                                            columnNumber: 39\n                                                                                        }, this),\n                                                                                        \"Fixing...\"\n                                                                                    ]\n                                                                                }, void 0, true) : \"Fix\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                                lineNumber: 620,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 619,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, event.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                        open: expanded.mismatched,\n                                        onOpenChange: ()=>toggleExpanded('mismatched'),\n                                        className: \"border rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Events with Mismatched Musician Counts (\",\n                                                                    mismatchedEvents.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    expanded.mismatched ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                className: \"px-4 pb-4\",\n                                                children: mismatchedEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground py-2\",\n                                                    children: \"No events with mismatched musician counts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Event Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 669,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 670,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Venue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 671,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Azure Talent IDs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 672,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"PostgreSQL Talents\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 673,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Action\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 668,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                            children: mismatchedEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            className: \"font-medium\",\n                                                                            children: event.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: formatDate(event.date)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 681,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: event.venue_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: event.azure_talent_ids.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 683,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            className: \"text-yellow-500\",\n                                                                            children: event.postgres_talent_count\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>openFixConfirmDialog(event),\n                                                                                disabled: fixingEvents[event.id],\n                                                                                children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                            className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                                            lineNumber: 694,\n                                                                                            columnNumber: 39\n                                                                                        }, this),\n                                                                                        \"Fixing...\"\n                                                                                    ]\n                                                                                }, void 0, true) : \"Fix\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                                lineNumber: 686,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, event.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                lineNumber: 460,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: confirmDialogOpen,\n                onOpenChange: setConfirmDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                    children: \"Fix Event-Musician Relationship\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                                    children: selectedEvent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 py-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Are you sure you want to fix the musician relationships for this event?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted p-3 rounded-md text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Event:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            selectedEvent.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Date:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            formatDate(selectedEvent.date)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Venue:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 728,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            selectedEvent.venue_name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Azure Talents:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            selectedEvent.azure_talent_ids.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"PostgreSQL Talents:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            selectedEvent.postgres_talent_count\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 725,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"This will:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-5 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Retrieve talent relationships from Azure SQL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Update the PostgreSQL database to match\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Add missing talent relationships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Remove incorrect talent relationships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 718,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setConfirmDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: handleFixSingleEvent,\n                                    children: \"Fix\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                    lineNumber: 717,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                lineNumber: 716,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n        lineNumber: 452,\n        columnNumber: 5\n    }, this);\n}\n_s(EventTalentRelations, \"s5EYwvXrK6FOy5+KAwWcUYbv0pk=\", false, function() {\n    return [\n        _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = EventTalentRelations;\nvar _c;\n$RefreshReg$(_c, \"EventTalentRelations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/event-relations/talent.tsx\n"));

/***/ })

});