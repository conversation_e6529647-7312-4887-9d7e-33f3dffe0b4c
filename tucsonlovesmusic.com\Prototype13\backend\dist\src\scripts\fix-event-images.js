"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processEventImagesFromAzure = processEventImagesFromAzure;
const dotenv = __importStar(require("dotenv"));
const path = __importStar(require("path"));
const axios_1 = __importDefault(require("axios"));
const client_s3_1 = require("@aws-sdk/client-s3");
const mssql = __importStar(require("mssql"));
const events_entity_1 = require("../events/events.entity");
dotenv.config();
process.env.AWS_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID || 'dummy-key';
process.env.AWS_SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY || 'dummy-secret';
process.env.AWS_REGION = process.env.AWS_REGION || 'us-west-2';
process.env.AWS_S3_BUCKET = process.env.AWS_S3_BUCKET || 'tucsonlovesmusic';
const azureConfig = {
    server: 'mssql.drv1.umbhost.net',
    database: 'TLM',
    user: 'Reader',
    password: 'TLM1234!',
    options: {
        encrypt: false
    }
};
async function processEventImagesFromAzure(dataSource, dryRun = false) {
    console.log('Starting event image processing from Azure...');
    console.log(`Mode: ${dryRun ? 'DRY RUN' : 'EXECUTE'}`);
    let azurePool = null;
    try {
        console.log('Using provided DataSource for PostgreSQL connection');
        azurePool = await mssql.connect(azureConfig);
        console.log('Azure SQL connection initialized');
        const s3Client = new client_s3_1.S3Client({
            region: process.env.AWS_REGION || 'us-west-2',
            credentials: {
                accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || ''
            }
        });
        const BUCKET_NAME = process.env.AWS_S3_BUCKET || 'tucsonlovesmusic';
        const stats = {
            total: 0,
            processed: 0,
            success: 0,
            errors: 0,
            skipped: 0,
            notFound: 0
        };
        const fixedEvents = [];
        const azureResult = await azurePool.request()
            .query(`
        SELECT 
            pe.Id, 
            pe.Name, 
            pe.ProfileImageCropperValue,
            pe.ProfileImageId,
            un.text as MediaName,
            un.uniqueId as MediaGuid,
            pd.textValue as MediaPath
        FROM PerformanceEvents pe
        LEFT JOIN umbracoNode un ON pe.ProfileImageId = un.id 
            AND un.nodeObjectType = 'b796f64c-1f99-4ffb-b886-4bf4bc011a9c'
        LEFT JOIN umbracoContentVersion cv ON un.id = cv.nodeId
        LEFT JOIN umbracoPropertyData pd ON cv.id = pd.versionId 
            AND pd.propertyTypeId IN (
                SELECT id FROM cmsPropertyType 
                WHERE alias = 'umbracoFile'
            )
        -- Removed restrictive WHERE clause to include all events
        -- The script logic will handle events without image sources by skipping them
      `);
        const azureEvents = azureResult.recordset;
        stats.total = azureEvents.length;
        console.log(`Found ${azureEvents.length} events with image data in Azure SQL`);
        const BASE_URL = process.env.MEDIA_SOURCE_URL || 'https://tucsonlovesmusic.com';
        for (const azureEvent of azureEvents) {
            try {
                let sourceUrl = null;
                let imageSource = 'none';
                if (azureEvent.ProfileImageCropperValue) {
                    try {
                        const imageData = JSON.parse(azureEvent.ProfileImageCropperValue);
                        if (imageData && imageData.Src) {
                            sourceUrl = imageData.Src.startsWith('http') ?
                                imageData.Src : `${BASE_URL}${imageData.Src}`;
                            imageSource = 'cropper';
                        }
                    }
                    catch (error) {
                        console.warn(`Could not parse ProfileImageCropperValue for event ${azureEvent.Name} (${azureEvent.Id}): ${error instanceof Error ? error.message : 'Unknown error'}`);
                    }
                }
                if (!sourceUrl && azureEvent.MediaPath) {
                    try {
                        const mediaData = JSON.parse(azureEvent.MediaPath);
                        if (mediaData && mediaData.Src) {
                            sourceUrl = mediaData.Src.startsWith('http') ?
                                mediaData.Src : `${BASE_URL}${mediaData.Src}`;
                            imageSource = 'umbraco';
                        }
                    }
                    catch (jsonError) {
                        if (azureEvent.MediaPath.startsWith('http')) {
                            sourceUrl = azureEvent.MediaPath;
                            imageSource = 'direct';
                        }
                        else if (azureEvent.MediaPath.includes('/media/')) {
                            const mediaMatch = azureEvent.MediaPath.match(/\/media\/[^\s"'}]+/);
                            if (mediaMatch) {
                                sourceUrl = `${BASE_URL}${mediaMatch[0]}`;
                                imageSource = 'direct';
                            }
                        }
                        else {
                            sourceUrl = azureEvent.MediaPath.startsWith('http') ?
                                azureEvent.MediaPath : `${BASE_URL}${azureEvent.MediaPath}`;
                            imageSource = 'direct';
                        }
                    }
                }
                if (!sourceUrl) {
                    console.log(`⚠️  No ProfileImageId source for event: ${azureEvent.Name} (${azureEvent.Id}), searching by name...`);
                    const nameSearchResult = await azurePool.request().query(`
            SELECT TOP 1
              un.id,
              un.text,
              un.uniqueId,
              pd.textValue
            FROM umbracoNode un
            INNER JOIN umbracoContentVersion cv ON un.id = cv.nodeId AND cv.[current] = 1
            INNER JOIN umbracoPropertyData pd ON cv.id = pd.versionId
            INNER JOIN cmsPropertyType pt ON pd.propertyTypeId = pt.id
            WHERE (un.text LIKE '%${azureEvent.Name.replace(/'/g, "''")}%' 
                   OR pd.textValue LIKE '%${azureEvent.Name.replace(/'/g, "''")}%')
              AND pt.alias = 'umbracoFile'
              AND pd.textValue IS NOT NULL
            ORDER BY un.id DESC
          `);
                    if (nameSearchResult.recordset.length > 0) {
                        const mediaNode = nameSearchResult.recordset[0];
                        console.log(`✓ Found media by name: ${mediaNode.text} (ID: ${mediaNode.id})`);
                        try {
                            const mediaData = JSON.parse(mediaNode.textValue);
                            if (mediaData && mediaData.Src) {
                                sourceUrl = mediaData.Src.startsWith('http') ?
                                    mediaData.Src : `${BASE_URL}${mediaData.Src}`;
                                imageSource = 'name-search';
                            }
                        }
                        catch (jsonError) {
                            if (mediaNode.textValue.includes('/media/')) {
                                const mediaMatch = mediaNode.textValue.match(/\/media\/[^\s"'}]+/);
                                if (mediaMatch) {
                                    sourceUrl = `${BASE_URL}${mediaMatch[0]}`;
                                    imageSource = 'name-search-direct';
                                }
                            }
                        }
                    }
                }
                if (!sourceUrl) {
                    console.log(`❌ No image source found for event: ${azureEvent.Name} (${azureEvent.Id})`);
                    stats.skipped++;
                    continue;
                }
                const eventRepository = dataSource.getRepository(events_entity_1.Event);
                const pgEvent = await eventRepository.findOne({
                    where: { azure_id: azureEvent.Id },
                    select: ['id', 'name', 'imageUrl']
                });
                if (!pgEvent) {
                    console.warn(`No matching PostgreSQL event found for Azure ID: ${azureEvent.Id}`);
                    stats.notFound++;
                    continue;
                }
                if (pgEvent.imageUrl && pgEvent.imageUrl.trim() !== '') {
                    console.log(`Event ${pgEvent.name} (${pgEvent.id}) already has an image URL: ${pgEvent.imageUrl}`);
                    stats.skipped++;
                    continue;
                }
                console.log(`\n📸 Processing: ${azureEvent.Name} (${azureEvent.Id})`);
                console.log(`Source: ${sourceUrl}`);
                console.log(`Image source method: ${imageSource}`);
                if (imageSource.includes('name-search')) {
                    console.log(`🔍 Found via name search fallback (ProfileImageId was NULL)`);
                }
                const upperEventId = azureEvent.Id.toUpperCase();
                const s3Key = `events/${upperEventId}.jpg`;
                let exists = false;
                try {
                    await s3Client.send(new client_s3_1.HeadObjectCommand({
                        Bucket: BUCKET_NAME,
                        Key: s3Key
                    }));
                    exists = true;
                }
                catch (error) {
                    exists = false;
                }
                if (exists) {
                    console.log(`Image already exists in S3 for event ${pgEvent.name} (${pgEvent.id})`);
                    const s3Url = `https://${BUCKET_NAME}.s3.${process.env.AWS_REGION || 'us-west-2'}.amazonaws.com/${s3Key}`;
                    if (!dryRun) {
                        await eventRepository.update(pgEvent.id, { imageUrl: s3Url });
                    }
                    console.log(`${dryRun ? '[DRY RUN] Would update' : 'Updated'} event ${pgEvent.name} (${pgEvent.id}) with existing S3 URL: ${s3Url}`);
                    stats.success++;
                    fixedEvents.push({
                        id: pgEvent.id,
                        name: pgEvent.name || 'Unknown Event',
                        imageUrl: s3Url
                    });
                }
                else {
                    console.log(`Fetching image from ${sourceUrl}`);
                    try {
                        const response = await axios_1.default.get(sourceUrl, {
                            responseType: 'arraybuffer',
                            headers: {
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                            }
                        });
                        if (response.status === 200) {
                            console.log(`Uploading image for event ${pgEvent.name} (${pgEvent.id}) to S3...`);
                            try {
                                await s3Client.send(new client_s3_1.PutObjectCommand({
                                    Bucket: BUCKET_NAME,
                                    Key: s3Key,
                                    Body: Buffer.from(response.data),
                                    ContentType: response.headers['content-type'] || 'image/jpeg',
                                    ACL: 'public-read'
                                }));
                                const s3Url = `https://${BUCKET_NAME}.s3.${process.env.AWS_REGION || 'us-west-2'}.amazonaws.com/${s3Key}`;
                                if (!dryRun) {
                                    await eventRepository.update(pgEvent.id, { imageUrl: s3Url });
                                }
                                console.log(`${dryRun ? '[DRY RUN] Would update' : 'Successfully updated'} event ${pgEvent.name} (${pgEvent.id}) with S3 URL: ${s3Url}`);
                                stats.success++;
                                fixedEvents.push({
                                    id: pgEvent.id,
                                    name: pgEvent.name || 'Unknown Event',
                                    imageUrl: s3Url
                                });
                            }
                            catch (uploadError) {
                                console.error(`Error uploading to S3: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`);
                                stats.errors++;
                            }
                        }
                        else {
                            throw new Error(`Failed to fetch image: HTTP ${response.status}`);
                        }
                    }
                    catch (error) {
                        console.error(`Error fetching/uploading image for event ${pgEvent.name} (${pgEvent.id}): ${error instanceof Error ? error.message : 'Unknown error'}`);
                        stats.errors++;
                        continue;
                    }
                }
            }
            catch (error) {
                console.error(`Error processing event ${azureEvent.Name} (${azureEvent.Id}): ${error instanceof Error ? error.message : 'Unknown error'}`);
                stats.errors++;
            }
            finally {
                stats.processed++;
                if (stats.processed % 10 === 0 || stats.processed === stats.total) {
                    console.log(`Progress: ${stats.processed}/${stats.total} (${Math.round((stats.processed / stats.total) * 100)}%)`);
                }
            }
        }
        console.log('\n=== IMAGE PROCESSING SUMMARY ===');
        console.log(`Total events found in Azure: ${stats.total}`);
        console.log(`Events processed: ${stats.processed}`);
        console.log(`Successfully updated: ${stats.success}`);
        console.log(`Skipped (has image or no valid source): ${stats.skipped}`);
        console.log(`Not found in PostgreSQL: ${stats.notFound}`);
        console.log(`Errors encountered: ${stats.errors}`);
        return {
            total: stats.total,
            processed: stats.processed,
            success: stats.success,
            skipped: stats.skipped,
            errors: stats.errors,
            fixedEvents: fixedEvents
        };
    }
    catch (error) {
        console.error(`Error in process: ${error instanceof Error ? error.message : 'Unknown error'}`);
        if (error instanceof Error && error.stack) {
            console.error(`Error stack: ${error.stack}`);
        }
        throw error;
    }
    finally {
        if (azurePool) {
            await azurePool.close();
            console.log('Azure SQL connection closed');
        }
    }
}
async function bootstrap() {
    console.log('Starting event image fix script (Azure to S3)...');
    console.log('Environment variables loaded from:', path.resolve(__dirname, '../../.env'));
    const { createConnection } = require('typeorm');
    try {
        const dbUrl = process.env.DATABASE_URL;
        if (!dbUrl) {
            throw new Error('DATABASE_URL environment variable is not set');
        }
        const dataSource = await createConnection({
            type: 'postgres',
            url: dbUrl,
            entities: [events_entity_1.Event],
            synchronize: false,
            ssl: { rejectUnauthorized: false },
            extra: {
                max: 10,
                idleTimeoutMillis: 30000,
                connectionTimeoutMillis: 10000
            },
            logging: false
        });
        const result = await processEventImagesFromAzure(dataSource, false);
        console.log('Script completed successfully');
        await dataSource.close();
        console.log('PostgreSQL connection closed');
    }
    catch (error) {
        console.error('Error running script:', error);
    }
}
if (require.main === module) {
    bootstrap();
}
//# sourceMappingURL=fix-event-images.js.map