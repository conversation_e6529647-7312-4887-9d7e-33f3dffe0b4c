"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AdminEventsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminEventsController = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const admin_guard_1 = require("../../auth/admin.guard");
const auth_guard_1 = require("../../auth/auth.guard");
const events_service_1 = require("../events.service");
const events_entity_1 = require("../events.entity");
const reset_service_1 = require("../../database/reset.service");
const migration_modules_1 = require("../../database/migration-modules");
const sql = __importStar(require("mssql"));
let AdminEventsController = AdminEventsController_1 = class AdminEventsController {
    constructor(eventsService, eventRepository, dataSource, databaseResetService) {
        this.eventsService = eventsService;
        this.eventRepository = eventRepository;
        this.dataSource = dataSource;
        this.databaseResetService = databaseResetService;
        this.logger = new common_1.Logger(AdminEventsController_1.name);
    }
    async createAzureConnection() {
        const connectionString = process.env.AZURE_SQL_CONNECTION_STRING;
        let azureConnection;
        if (connectionString) {
            azureConnection = new sql.ConnectionPool(connectionString);
        }
        else {
            const azureConfig = {
                user: process.env.AZURE_SQL_USER,
                password: process.env.AZURE_SQL_PASSWORD,
                server: process.env.AZURE_SQL_SERVER,
                database: process.env.AZURE_SQL_DATABASE,
                options: {
                    encrypt: true,
                    trustServerCertificate: false
                },
                pool: {
                    max: 10,
                    min: 0,
                    idleTimeoutMillis: 30000
                }
            };
            azureConnection = new sql.ConnectionPool(azureConfig);
        }
        await azureConnection.connect();
        return azureConnection;
    }
    async checkEventImages() {
        this.logger.log('Checking event images');
        const events = await this.eventRepository.find({
            select: ['id', 'name', 'imageUrl'],
        });
        this.logger.debug(`Found ${events.length} total events`);
        this.logger.debug('Sample events:', events.slice(0, 5));
        const eventsWithImages = events.filter(e => e.imageUrl && e.imageUrl !== '');
        const eventsWithoutImages = events.filter(e => !e.imageUrl || e.imageUrl === '');
        return {
            totalEvents: events.length,
            eventsWithImages: eventsWithImages.length,
            eventsWithoutImages: eventsWithoutImages.length,
            events: eventsWithoutImages.map(e => ({
                id: e.id,
                name: e.name,
                imageUrl: e.imageUrl
            }))
        };
    }
    async fixEventImages() {
        this.logger.log('Event image fix endpoint called');
        this.logger.warn('Event image fix should be run manually via npm script');
        return {
            total: 0,
            processed: 0,
            success: 0,
            skipped: 0,
            errors: 0,
            message: 'Please run "npm run fix-event-images" manually in the terminal. The API endpoint has been disabled to prevent database connection conflicts and server restart loops.'
        };
    }
    async resetEventsTable() {
        this.logger.log('Starting events table reset');
        try {
            await this.databaseResetService.resetDatabase(['event']);
            this.logger.log('Events table reset completed successfully');
            return {
                success: true,
                message: 'Events table reset completed successfully'
            };
        }
        catch (error) {
            this.logger.error(`Error resetting events table: ${error.message}`);
            throw error;
        }
    }
    async triggerFullMigration() {
        this.logger.log('Starting full migration with enhanced duplicate detection...');
        let azureConnection = null;
        try {
            azureConnection = await this.createAzureConnection();
            this.logger.log('Azure connection established for full migration');
            const migrationConfig = {
                skipDuplicates: true,
                enableFuzzyDuplicateDetection: true,
                batchSize: 50,
                maxRetries: 3,
                venueDuplicateDetection: {
                    nameThreshold: 2,
                    addressThreshold: 3,
                    proximityThresholdKm: 0.3,
                    enableFuzzyMatching: true,
                    enableGeographicMatching: true
                }
            };
            const orchestrator = new migration_modules_1.MigrationOrchestrator(this.dataSource, migrationConfig);
            this.logger.log('Full migration orchestrator initialized with enhanced duplicate detection, starting migration...');
            const summary = await orchestrator.migrateNewAzureEvents(azureConnection, true, undefined, false);
            try {
                this.logger.log('Starting post-migration event image fix');
                const imageFixResult = await this.fixEventImages();
                this.logger.log(`Post-migration event image fix completed: ${JSON.stringify(imageFixResult)}`);
            }
            catch (err) {
                this.logger.error(`Post-migration image fix failed: ${err?.message || err}`);
            }
            this.logger.log('Full migration completed successfully');
            return summary;
        }
        finally {
            if (azureConnection) {
                await azureConnection.close();
                this.logger.log('Azure connection closed');
            }
        }
    }
    async triggerLimitedMigration(body) {
        const { limit = 1000, prioritizeUpcoming = true } = body;
        this.logger.log(`Starting limited migration with enhanced duplicate detection - limit: ${limit}, prioritizeUpcoming: ${prioritizeUpcoming}`);
        let azureConnection = null;
        try {
            azureConnection = await this.createAzureConnection();
            this.logger.log('Azure connection established for limited migration');
            const migrationConfig = {
                skipDuplicates: true,
                enableFuzzyDuplicateDetection: true,
                batchSize: 100,
                maxRetries: 3,
                venueDuplicateDetection: {
                    nameThreshold: 2,
                    addressThreshold: 3,
                    proximityThresholdKm: 0.3,
                    enableFuzzyMatching: true,
                    enableGeographicMatching: true
                }
            };
            const orchestrator = new migration_modules_1.MigrationOrchestrator(this.dataSource, migrationConfig);
            this.logger.log('Limited migration orchestrator initialized with enhanced duplicate detection, starting migration...');
            const summary = await orchestrator.migrateNewAzureEvents(azureConnection, false, limit, prioritizeUpcoming);
            try {
                this.logger.log('Starting post-limited-migration event image fix');
                const imageFixResult = await this.fixEventImages();
                this.logger.log(`Post-limited-migration event image fix completed: ${JSON.stringify(imageFixResult)}`);
            }
            catch (err) {
                this.logger.error(`Post-limited-migration image fix failed: ${err?.message || err}`);
            }
            this.logger.log('Limited migration completed successfully');
            return summary;
        }
        catch (error) {
            this.logger.error(`Error during limited migration: ${error.message}`);
            throw error;
        }
        finally {
            if (azureConnection) {
                await azureConnection.close();
                this.logger.log('Azure connection closed');
            }
        }
    }
    async getMigrationStatus() {
        this.logger.log('Fetching migration status');
        try {
            const lastMigrationResult = await this.dataSource.query(`
        SELECT last_migration_timestamp FROM azure_migration_tracking 
        WHERE migration_type = 'events' 
        ORDER BY last_migration_timestamp DESC 
        LIMIT 1
      `);
            const lastMigration = lastMigrationResult.length > 0
                ? lastMigrationResult[0].last_migration_timestamp
                : null;
            const totalEvents = await this.eventRepository.count();
            return {
                lastMigration,
                totalEvents,
                migrationModulesVersion: '1.0.0'
            };
        }
        catch (error) {
            this.logger.error(`Error fetching migration status: ${error.message}`);
            throw error;
        }
    }
};
exports.AdminEventsController = AdminEventsController;
__decorate([
    (0, common_1.Get)('diagnostics/check-images'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminEventsController.prototype, "checkEventImages", null);
__decorate([
    (0, common_1.Post)('sync/fix-event-images'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminEventsController.prototype, "fixEventImages", null);
__decorate([
    (0, common_1.Post)('migration/reset-events-table'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminEventsController.prototype, "resetEventsTable", null);
__decorate([
    (0, common_1.Post)('migration/trigger-full-migration'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminEventsController.prototype, "triggerFullMigration", null);
__decorate([
    (0, common_1.Post)('migration/trigger-limited-migration'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminEventsController.prototype, "triggerLimitedMigration", null);
__decorate([
    (0, common_1.Get)('migration/status'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminEventsController.prototype, "getMigrationStatus", null);
exports.AdminEventsController = AdminEventsController = AdminEventsController_1 = __decorate([
    (0, common_1.Controller)('admin/events'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard, admin_guard_1.AdminGuard),
    __param(1, (0, typeorm_1.InjectRepository)(events_entity_1.Event)),
    __metadata("design:paramtypes", [events_service_1.EventsService,
        typeorm_2.Repository,
        typeorm_2.DataSource,
        reset_service_1.DatabaseResetService])
], AdminEventsController);
//# sourceMappingURL=admin-events.controller.js.map