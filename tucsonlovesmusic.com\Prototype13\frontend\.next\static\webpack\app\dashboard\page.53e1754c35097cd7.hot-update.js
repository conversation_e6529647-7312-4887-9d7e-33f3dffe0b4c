"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/admin/event-relations/venue.tsx":
/*!****************************************************!*\
  !*** ./components/admin/event-relations/venue.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventVenueRelations: () => (/* binding */ EventVenueRelations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-auth */ \"(app-pages-browser)/./hooks/use-auth.ts\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ EventVenueRelations auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction EventVenueRelations() {\n    _s();\n    const [counts, setCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [missingEvents, setMissingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mismatchedEvents, setMismatchedEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isBulkFixing, setIsBulkFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [batchFixing, setBatchFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [csvFixing, setCsvFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [batchFixProgress, setBatchFixProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 0,\n        total: 0,\n        success: 0,\n        failed: 0\n    });\n    const [fixingEvents, setFixingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        missing: false,\n        mismatched: false\n    });\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { getAccessToken } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // Fetch data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventVenueRelations.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"EventVenueRelations.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            setIsLoading(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Fetch counts\n            const countsResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!countsResponse.ok) {\n                throw new Error('Failed to fetch event-venue relationship counts');\n            }\n            const countsData = await countsResponse.json();\n            setCounts(countsData);\n            // Fetch missing events\n            const missingResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/missing\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (missingResponse.ok) {\n                const missingData = await missingResponse.json();\n                setMissingEvents(missingData);\n            }\n            // Fetch mismatched events\n            const mismatchedResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/mismatched\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (mismatchedResponse.ok) {\n                const mismatchedData = await mismatchedResponse.json();\n                setMismatchedEvents(mismatchedData);\n            }\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching event-venue relationship data', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch event-venue relationship data');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleExpanded = (section)=>{\n        setExpanded((prev)=>({\n                ...prev,\n                [section]: !prev[section]\n            }));\n    };\n    const handleFixAll = async ()=>{\n        try {\n            setIsBulkFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix-all\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fix all event-venue relationships');\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: result.message,\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing all event-venue relationships', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to fix all event-venue relationships',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsBulkFixing(false);\n        }\n    };\n    // Handle batch fixing a limited number of events\n    const handleBatchFix = async ()=>{\n        try {\n            setBatchFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Determine which events to fix - prioritize missing events\n            const eventsToFix = [\n                ...missingEvents\n            ].slice(0, 10); // Limit to 10 events at a time\n            if (eventsToFix.length === 0) {\n                toast({\n                    title: 'No Events to Fix',\n                    description: 'There are no events that need fixing.',\n                    variant: 'default'\n                });\n                return;\n            }\n            // Set up progress tracking\n            setBatchFixProgress({\n                current: 0,\n                total: eventsToFix.length,\n                success: 0,\n                failed: 0\n            });\n            // Mark all events as being fixed\n            const newFixingState = {};\n            eventsToFix.forEach((event)=>{\n                newFixingState[event.id] = true;\n            });\n            setFixingEvents((prev)=>({\n                    ...prev,\n                    ...newFixingState\n                }));\n            console.log(\"\\uD83D\\uDD27 [Frontend] Starting batch fix for \".concat(eventsToFix.length, \" venue relationships\"));\n            // Process events sequentially to avoid overwhelming the server\n            for(let i = 0; i < eventsToFix.length; i++){\n                const event = eventsToFix[i];\n                setBatchFixProgress((prev)=>({\n                        ...prev,\n                        current: i + 1\n                    }));\n                try {\n                    console.log(\"\\uD83D\\uDD27 [Frontend] Fixing event \".concat(i + 1, \"/\").concat(eventsToFix.length, \": \").concat(event.name, \" (ID: \").concat(event.id, \")\"));\n                    // Create the request body with eventId and venueId\n                    const requestBody = {\n                        eventId: event.id,\n                        venueId: event.azure_venue_id // Using the Azure venue ID from the selected event\n                    };\n                    const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix\"), {\n                        method: 'POST',\n                        headers: {\n                            'Authorization': \"Bearer \".concat(token),\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify(requestBody)\n                    });\n                    if (!response.ok) {\n                        console.error(\"\\uD83D\\uDD27 [Frontend] Failed to fix event \".concat(event.name, \": \").concat(response.status));\n                        setBatchFixProgress((prev)=>({\n                                ...prev,\n                                failed: prev.failed + 1\n                            }));\n                    } else {\n                        console.log(\"\\uD83D\\uDD27 [Frontend] Successfully fixed event \".concat(event.name));\n                        setBatchFixProgress((prev)=>({\n                                ...prev,\n                                success: prev.success + 1\n                            }));\n                    }\n                } catch (eventError) {\n                    console.error(\"\\uD83D\\uDD27 [Frontend] Error fixing event \".concat(event.name, \":\"), eventError);\n                    setBatchFixProgress((prev)=>({\n                            ...prev,\n                            failed: prev.failed + 1\n                        }));\n                } finally{\n                    // Mark this event as no longer being fixed\n                    setFixingEvents((prev)=>({\n                            ...prev,\n                            [event.id]: false\n                        }));\n                }\n                // Small delay to avoid overwhelming the server\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n            }\n            toast({\n                title: 'Batch Fix Complete',\n                description: \"Successfully fixed \".concat(batchFixProgress.success, \" out of \").concat(eventsToFix.length, \" venue relationships.\"),\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error during batch fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete batch fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setBatchFixing(false);\n            // Reset progress\n            setBatchFixProgress({\n                current: 0,\n                total: 0,\n                success: 0,\n                failed: 0\n            });\n        }\n    };\n    // Handle CSV-based fix for all event-venue relationships\n    const handleCsvFix = async ()=>{\n        try {\n            var _result_stats;\n            setCsvFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            console.log('🔧 [Frontend] Starting CSV-based fix for event-venue relationships');\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix-from-csv\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('🔧 [Frontend] Server error response:', errorText);\n                throw new Error(\"Failed to execute CSV fix: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('🔧 [Frontend] CSV fix completed:', result);\n            toast({\n                title: 'CSV Fix Complete',\n                description: \"Validated \".concat(((_result_stats = result.stats) === null || _result_stats === void 0 ? void 0 : _result_stats.validatedRelationships) || 0, \" venue relationships from CSV\"),\n                variant: 'default'\n            });\n            // Refresh data after CSV fix\n            fetchData();\n        } catch (err) {\n            console.error('🔧 [Frontend] Error during CSV fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete CSV fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setCsvFixing(false);\n        }\n    };\n    // Open confirmation dialog for fixing an event\n    const openFixConfirmDialog = (event)=>{\n        setSelectedEvent(event);\n        setConfirmDialogOpen(true);\n    };\n    // Handle fixing a single event's venue relationship through the new confirmation dialog\n    const handleFixEventVenue = async ()=>{\n        if (!selectedEvent) return;\n        try {\n            // Close the dialog\n            setConfirmDialogOpen(false);\n            // Mark this event as being fixed\n            setFixingEvents((prev)=>({\n                    ...prev,\n                    [selectedEvent.id]: true\n                }));\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Create the request body with eventId and venueId\n            const requestBody = {\n                eventId: selectedEvent.id,\n                venueId: selectedEvent.azure_venue_id // Using the Azure venue ID from the selected event\n            };\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to fix event-venue relationship for \".concat(selectedEvent.name));\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: 'Event \"'.concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name, '\" venue updated to \"').concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.azure_venue_name, '\"'),\n                variant: 'default'\n            });\n            // Refresh data after successful fix\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing event-venue relationship', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : \"Failed to fix event-venue relationship for \".concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name),\n                variant: 'destructive'\n            });\n        } finally{\n            // Clear the fixing state for this event\n            if (selectedEvent) {\n                setFixingEvents((prev)=>({\n                        ...prev,\n                        [selectedEvent.id]: false\n                    }));\n            }\n            // Clear the selected event\n            setSelectedEvent(null);\n        }\n    };\n    // Original function that was used with the FixVenueDialog component\n    // Now adapted to work with our centralized confirmation dialog\n    async function handleFixEventVenue_legacy(eventId, azureVenueId) {\n        try {\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    eventId,\n                    venueId: azureVenueId\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fix event-venue relationship');\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: 'Event venue relationship fixed successfully',\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing event-venue relationship', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to fix event-venue relationship',\n                variant: 'destructive'\n            });\n        }\n    }\n    // Determine status message and icon color\n    const getStatusDetails = ()=>{\n        if (!counts) return {\n            message: 'Loading...',\n            status: 'neutral'\n        };\n        // Check if there are any actual problematic relationships in the dropdown lists\n        const hasProblematicRelations = missingEvents.length > 0 || mismatchedEvents.length > 0;\n        // If there are no problematic relations to troubleshoot, show green\n        // Only show yellow warning when there's something that needs attention\n        if (!hasProblematicRelations) {\n            return {\n                message: counts.missingVenues === 0 ? 'All events have venue relationships' : \"\".concat(counts.missingVenues, \" events with no venue relations\"),\n                status: 'good'\n            };\n        }\n        // Show yellow warning when there are items in the dropdown lists that need attention\n        return {\n            message: \"\".concat(counts.missingVenues, \" events with no venue relations\"),\n            status: 'warning'\n        };\n    };\n    // Get the appropriate icon color based on status\n    const getIconColorClass = (status)=>{\n        switch(status){\n            case 'good':\n                return 'text-green-500';\n            case 'warning':\n                return 'text-yellow-500';\n            case 'bad':\n                return 'text-red-500';\n            default:\n                return 'text-muted-foreground';\n        }\n    };\n    const statusDetails = getStatusDetails();\n    // Format date for display\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'No date';\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"flex flex-row items-center space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-5 w-5 mr-2 \".concat(getIconColorClass(statusDetails.status))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-xl\",\n                            children: \"Events - Venues\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 498,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                lineNumber: 495,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-8 w-8 animate-spin text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-muted-foreground\",\n                                children: \"Loading relationship data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 11\n                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8 px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5 text-red-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-500\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: statusDetails.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            onClick: handleBatchFix,\n                                            disabled: batchFixing || csvFixing || missingEvents.length === 0,\n                                            className: \"flex items-center\",\n                                            variant: \"outline\",\n                                            children: batchFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Fixing \",\n                                                    batchFixProgress.current,\n                                                    \"/\",\n                                                    batchFixProgress.total,\n                                                    \"...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Batch Fix (10 Events)\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            onClick: handleCsvFix,\n                                            disabled: batchFixing || csvFixing,\n                                            className: \"flex items-center\",\n                                            variant: \"outline\",\n                                            children: csvFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Validating CSV...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Validate from CSV\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"icon\",\n                                            onClick: fetchData,\n                                            disabled: isLoading || batchFixing || csvFixing,\n                                            title: \"Refresh data\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 15\n                                }, this),\n                                batchFixing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-muted rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm mb-1 flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Batch Fix Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        batchFixProgress.current,\n                                                        \"/\",\n                                                        batchFixProgress.total,\n                                                        \" events\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-secondary h-2 rounded-full overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary h-full transition-all duration-300 ease-in-out\",\n                                                style: {\n                                                    width: \"\".concat(batchFixProgress.current / Math.max(batchFixProgress.total, 1) * 100, \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between text-xs mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500\",\n                                                    children: [\n                                                        batchFixProgress.success,\n                                                        \" successful\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: [\n                                                        batchFixProgress.failed,\n                                                        \" failed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                            open: expanded.missing,\n                                            onOpenChange: ()=>toggleExpanded('missing'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 638,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Events Missing Venue Relationships (\",\n                                                                        missingEvents.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        expanded.missing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: missingEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No events missing venues\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Event Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 655,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 656,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Azure Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 657,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"PostgreSQL Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 658,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 659,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                                children: missingEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"font-medium\",\n                                                                                children: event.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 665,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: formatDate(event.date)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 666,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: event.azure_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 667,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"text-red-500\",\n                                                                                children: event.postgres_venue_name || 'Missing'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 668,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>openFixConfirmDialog(event),\n                                                                                    disabled: fixingEvents[event.id],\n                                                                                    children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                                lineNumber: 678,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            \"Fixing...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Fix\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                    lineNumber: 670,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 669,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, event.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 664,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                            open: expanded.mismatched,\n                                            onOpenChange: ()=>toggleExpanded('mismatched'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Events with Mismatched Venue (\",\n                                                                        mismatchedEvents.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        expanded.mismatched ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 708,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: mismatchedEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No events with mismatched venues\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Event Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 719,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 720,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Azure Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 721,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"PostgreSQL Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 722,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 723,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 718,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                                children: mismatchedEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"font-medium\",\n                                                                                children: event.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 729,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: formatDate(event.date)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 730,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: event.azure_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 731,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"text-yellow-500\",\n                                                                                children: event.postgres_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 732,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>openFixConfirmDialog(event),\n                                                                                    disabled: fixingEvents[event.id],\n                                                                                    children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                                lineNumber: 742,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            \"Fixing...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Fix\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                    lineNumber: 734,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 733,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, event.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 728,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 695,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                        open: confirmDialogOpen,\n                        onOpenChange: setConfirmDialogOpen,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                            children: \"Fix Event-Venue Relationship\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                                            children: selectedEvent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Are you sure you want to fix the venue relationship for this event?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-muted p-3 rounded-md text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Event:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 773,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 773,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Date:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 774,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formatDate((selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.date) || '')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 774,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Current Venue:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 775,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    (selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.postgres_venue_name) || 'None'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Target Venue:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 776,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.azure_venue_name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"This will:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc pl-5 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Update the event to reference the correct venue from Azure\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Create the venue in PostgreSQL if it doesn't exist\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Update the event's venue_id to maintain data consistency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 767,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 765,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setConfirmDialogOpen(false),\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            onClick: handleFixEventVenue,\n                                            children: \"Fix\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 791,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 789,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 764,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 763,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n        lineNumber: 494,\n        columnNumber: 5\n    }, this);\n}\n_s(EventVenueRelations, \"vyrNPCcHdw+/JqHYqbEUKP4OiFA=\", false, function() {\n    return [\n        _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = EventVenueRelations;\nvar _c;\n$RefreshReg$(_c, \"EventVenueRelations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvYWRtaW4vZXZlbnQtcmVsYXRpb25zL3ZlbnVlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNvQztBQVcxRDtBQUNxQjtBQUtOO0FBQ1c7QUFRaEI7QUFRRDtBQUNjO0FBQ0o7QUFtQmxDLFNBQVNrQzs7SUFDZCxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR25DLCtDQUFRQSxDQUFnQztJQUNwRSxNQUFNLENBQUNvQyxlQUFlQyxpQkFBaUIsR0FBR3JDLCtDQUFRQSxDQUFzQixFQUFFO0lBQzFFLE1BQU0sQ0FBQ3NDLGtCQUFrQkMsb0JBQW9CLEdBQUd2QywrQ0FBUUEsQ0FBc0IsRUFBRTtJQUNoRixNQUFNLENBQUN3QyxXQUFXQyxhQUFhLEdBQUd6QywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUMwQyxjQUFjQyxnQkFBZ0IsR0FBRzNDLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzRDLGFBQWFDLGVBQWUsR0FBRzdDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzhDLFdBQVdDLGFBQWEsR0FBRy9DLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2dELGtCQUFrQkMsb0JBQW9CLEdBQUdqRCwrQ0FBUUEsQ0FBQztRQUFFa0QsU0FBUztRQUFHQyxPQUFPO1FBQUdDLFNBQVM7UUFBR0MsUUFBUTtJQUFFO0lBQ3ZHLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUd2RCwrQ0FBUUEsQ0FBMEIsQ0FBQztJQUMzRSxNQUFNLENBQUN3RCxPQUFPQyxTQUFTLEdBQUd6RCwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDMEQsVUFBVUMsWUFBWSxHQUFHM0QsK0NBQVFBLENBQTRDO1FBQ2xGNEQsU0FBUztRQUNUQyxZQUFZO0lBQ2Q7SUFDQSxNQUFNLENBQUNDLG1CQUFtQkMscUJBQXFCLEdBQUcvRCwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUNnRSxlQUFlQyxpQkFBaUIsR0FBR2pFLCtDQUFRQSxDQUEyQjtJQUM3RSxNQUFNLEVBQUVrRSxjQUFjLEVBQUUsR0FBR3BELHdEQUFPQTtJQUNsQyxNQUFNLEVBQUVxRCxLQUFLLEVBQUUsR0FBR3BDLDBEQUFRQTtJQUUxQixnQ0FBZ0M7SUFDaENoQyxnREFBU0E7eUNBQUM7WUFDUnFFO1FBQ0Y7d0NBQUcsRUFBRTtJQUVMLE1BQU1BLFlBQVk7UUFDaEIsSUFBSTtZQUNGM0IsYUFBYTtZQUNiLE1BQU00QixRQUFRLE1BQU1IO1lBQ3BCLE1BQU1JLFVBQVV0QyxzREFBU0E7WUFFekIsZUFBZTtZQUNmLE1BQU11QyxpQkFBaUIsTUFBTUMsTUFBTSxHQUFXLE9BQVJGLFNBQVEsaUNBQStCO2dCQUMzRUcsU0FBUztvQkFDUCxpQkFBaUIsVUFBZ0IsT0FBTko7Z0JBQzdCO1lBQ0Y7WUFFQSxJQUFJLENBQUNFLGVBQWVHLEVBQUUsRUFBRTtnQkFDdEIsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBRUEsTUFBTUMsYUFBYSxNQUFNTCxlQUFlTSxJQUFJO1lBQzVDMUMsVUFBVXlDO1lBRVYsdUJBQXVCO1lBQ3ZCLE1BQU1FLGtCQUFrQixNQUFNTixNQUFNLEdBQVcsT0FBUkYsU0FBUSx5Q0FBdUM7Z0JBQ3BGRyxTQUFTO29CQUNQLGlCQUFpQixVQUFnQixPQUFOSjtnQkFDN0I7WUFDRjtZQUVBLElBQUlTLGdCQUFnQkosRUFBRSxFQUFFO2dCQUN0QixNQUFNSyxjQUFjLE1BQU1ELGdCQUFnQkQsSUFBSTtnQkFDOUN4QyxpQkFBaUIwQztZQUNuQjtZQUVBLDBCQUEwQjtZQUMxQixNQUFNQyxxQkFBcUIsTUFBTVIsTUFBTSxHQUFXLE9BQVJGLFNBQVEsNENBQTBDO2dCQUMxRkcsU0FBUztvQkFDUCxpQkFBaUIsVUFBZ0IsT0FBTko7Z0JBQzdCO1lBQ0Y7WUFFQSxJQUFJVyxtQkFBbUJOLEVBQUUsRUFBRTtnQkFDekIsTUFBTU8saUJBQWlCLE1BQU1ELG1CQUFtQkgsSUFBSTtnQkFDcER0QyxvQkFBb0IwQztZQUN0QjtZQUVBeEIsU0FBUztRQUNYLEVBQUUsT0FBT3lCLEtBQWM7WUFDckJDLFFBQVEzQixLQUFLLENBQUMsZ0RBQWdEMEI7WUFDOUR6QixTQUFTeUIsZUFBZVAsUUFBUU8sSUFBSUUsT0FBTyxHQUFHO1FBQ2hELFNBQVU7WUFDUjNDLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTTRDLGlCQUFpQixDQUFDQztRQUN0QjNCLFlBQVk0QixDQUFBQSxPQUFTO2dCQUNuQixHQUFHQSxJQUFJO2dCQUNQLENBQUNELFFBQVEsRUFBRSxDQUFDQyxJQUFJLENBQUNELFFBQVE7WUFDM0I7SUFDRjtJQUVBLE1BQU1FLGVBQWU7UUFDbkIsSUFBSTtZQUNGN0MsZ0JBQWdCO1lBQ2hCLE1BQU0wQixRQUFRLE1BQU1IO1lBQ3BCLE1BQU1JLFVBQVV0QyxzREFBU0E7WUFFekIsTUFBTXlELFdBQVcsTUFBTWpCLE1BQU0sR0FBVyxPQUFSRixTQUFRLHlDQUF1QztnQkFDN0VvQixRQUFRO2dCQUNSakIsU0FBUztvQkFDUCxpQkFBaUIsVUFBZ0IsT0FBTko7b0JBQzNCLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUVBLElBQUksQ0FBQ29CLFNBQVNmLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBRUEsTUFBTWdCLFNBQVMsTUFBTUYsU0FBU1osSUFBSTtZQUVsQ1YsTUFBTTtnQkFDSnlCLE9BQU87Z0JBQ1BDLGFBQWFGLE9BQU9QLE9BQU87Z0JBQzNCVSxTQUFTO1lBQ1g7WUFFQSxlQUFlO1lBQ2YxQjtRQUNGLEVBQUUsT0FBT2MsS0FBYztZQUNyQkMsUUFBUTNCLEtBQUssQ0FBQyw4Q0FBOEMwQjtZQUU1RGYsTUFBTTtnQkFDSnlCLE9BQU87Z0JBQ1BDLGFBQWFYLGVBQWVQLFFBQVFPLElBQUlFLE9BQU8sR0FBRztnQkFDbERVLFNBQVM7WUFDWDtRQUNGLFNBQVU7WUFDUm5ELGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEsaURBQWlEO0lBQ2pELE1BQU1vRCxpQkFBaUI7UUFDckIsSUFBSTtZQUNGbEQsZUFBZTtZQUNmLE1BQU13QixRQUFRLE1BQU1IO1lBQ3BCLE1BQU1JLFVBQVV0QyxzREFBU0E7WUFFekIsNERBQTREO1lBQzVELE1BQU1nRSxjQUFjO21CQUFJNUQ7YUFBYyxDQUFDNkQsS0FBSyxDQUFDLEdBQUcsS0FBSywrQkFBK0I7WUFFcEYsSUFBSUQsWUFBWUUsTUFBTSxLQUFLLEdBQUc7Z0JBQzVCL0IsTUFBTTtvQkFDSnlCLE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2JDLFNBQVM7Z0JBQ1g7Z0JBQ0E7WUFDRjtZQUVBLDJCQUEyQjtZQUMzQjdDLG9CQUFvQjtnQkFDbEJDLFNBQVM7Z0JBQ1RDLE9BQU82QyxZQUFZRSxNQUFNO2dCQUN6QjlDLFNBQVM7Z0JBQ1RDLFFBQVE7WUFDVjtZQUVBLGlDQUFpQztZQUNqQyxNQUFNOEMsaUJBQTBDLENBQUM7WUFDakRILFlBQVlJLE9BQU8sQ0FBQ0MsQ0FBQUE7Z0JBQ2xCRixjQUFjLENBQUNFLE1BQU1DLEVBQUUsQ0FBQyxHQUFHO1lBQzdCO1lBQ0EvQyxnQkFBZ0JnQyxDQUFBQSxPQUFTO29CQUFFLEdBQUdBLElBQUk7b0JBQUUsR0FBR1ksY0FBYztnQkFBQztZQUV0RGhCLFFBQVFvQixHQUFHLENBQUMsa0RBQTJELE9BQW5CUCxZQUFZRSxNQUFNLEVBQUM7WUFFdkUsK0RBQStEO1lBQy9ELElBQUssSUFBSU0sSUFBSSxHQUFHQSxJQUFJUixZQUFZRSxNQUFNLEVBQUVNLElBQUs7Z0JBQzNDLE1BQU1ILFFBQVFMLFdBQVcsQ0FBQ1EsRUFBRTtnQkFDNUJ2RCxvQkFBb0JzQyxDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUVyQyxTQUFTc0QsSUFBSTtvQkFBRTtnQkFFdkQsSUFBSTtvQkFDRnJCLFFBQVFvQixHQUFHLENBQUMsd0NBQXVDUCxPQUFUUSxJQUFJLEdBQUUsS0FBMEJILE9BQXZCTCxZQUFZRSxNQUFNLEVBQUMsTUFBdUJHLE9BQW5CQSxNQUFNSSxJQUFJLEVBQUMsVUFBaUIsT0FBVEosTUFBTUMsRUFBRSxFQUFDO29CQUV0RyxtREFBbUQ7b0JBQ25ELE1BQU1JLGNBQWM7d0JBQ2xCQyxTQUFTTixNQUFNQyxFQUFFO3dCQUNqQk0sU0FBU1AsTUFBTVEsY0FBYyxDQUFDLG1EQUFtRDtvQkFDbkY7b0JBRUEsTUFBTXBCLFdBQVcsTUFBTWpCLE1BQU0sR0FBVyxPQUFSRixTQUFRLHFDQUFtQzt3QkFDekVvQixRQUFRO3dCQUNSakIsU0FBUzs0QkFDUCxpQkFBaUIsVUFBZ0IsT0FBTko7NEJBQzNCLGdCQUFnQjt3QkFDbEI7d0JBQ0F5QyxNQUFNQyxLQUFLQyxTQUFTLENBQUNOO29CQUN2QjtvQkFFQSxJQUFJLENBQUNqQixTQUFTZixFQUFFLEVBQUU7d0JBQ2hCUyxRQUFRM0IsS0FBSyxDQUFDLCtDQUFvRGlDLE9BQWZZLE1BQU1JLElBQUksRUFBQyxNQUFvQixPQUFoQmhCLFNBQVN3QixNQUFNO3dCQUNqRmhFLG9CQUFvQnNDLENBQUFBLE9BQVM7Z0NBQUUsR0FBR0EsSUFBSTtnQ0FBRWxDLFFBQVFrQyxLQUFLbEMsTUFBTSxHQUFHOzRCQUFFO29CQUNsRSxPQUFPO3dCQUNMOEIsUUFBUW9CLEdBQUcsQ0FBQyxvREFBcUQsT0FBWEYsTUFBTUksSUFBSTt3QkFDaEV4RCxvQkFBb0JzQyxDQUFBQSxPQUFTO2dDQUFFLEdBQUdBLElBQUk7Z0NBQUVuQyxTQUFTbUMsS0FBS25DLE9BQU8sR0FBRzs0QkFBRTtvQkFDcEU7Z0JBQ0YsRUFBRSxPQUFPOEQsWUFBWTtvQkFDbkIvQixRQUFRM0IsS0FBSyxDQUFDLDhDQUErQyxPQUFYNkMsTUFBTUksSUFBSSxFQUFDLE1BQUlTO29CQUNqRWpFLG9CQUFvQnNDLENBQUFBLE9BQVM7NEJBQUUsR0FBR0EsSUFBSTs0QkFBRWxDLFFBQVFrQyxLQUFLbEMsTUFBTSxHQUFHO3dCQUFFO2dCQUNsRSxTQUFVO29CQUNSLDJDQUEyQztvQkFDM0NFLGdCQUFnQmdDLENBQUFBLE9BQVM7NEJBQUUsR0FBR0EsSUFBSTs0QkFBRSxDQUFDYyxNQUFNQyxFQUFFLENBQUMsRUFBRTt3QkFBTTtnQkFDeEQ7Z0JBRUEsK0NBQStDO2dCQUMvQyxNQUFNLElBQUlhLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7WUFDbkQ7WUFFQWpELE1BQU07Z0JBQ0p5QixPQUFPO2dCQUNQQyxhQUFhLHNCQUF5REcsT0FBbkNoRCxpQkFBaUJJLE9BQU8sRUFBQyxZQUE2QixPQUFuQjRDLFlBQVlFLE1BQU0sRUFBQztnQkFDekZKLFNBQVM7WUFDWDtZQUVBLGVBQWU7WUFDZjFCO1FBQ0YsRUFBRSxPQUFPYyxLQUFjO1lBQ3JCQyxRQUFRM0IsS0FBSyxDQUFDLG9DQUFvQzBCO1lBRWxEZixNQUFNO2dCQUNKeUIsT0FBTztnQkFDUEMsYUFBYVgsZUFBZVAsUUFBUU8sSUFBSUUsT0FBTyxHQUFHO2dCQUNsRFUsU0FBUztZQUNYO1FBQ0YsU0FBVTtZQUNSakQsZUFBZTtZQUNmLGlCQUFpQjtZQUNqQkksb0JBQW9CO2dCQUFFQyxTQUFTO2dCQUFHQyxPQUFPO2dCQUFHQyxTQUFTO2dCQUFHQyxRQUFRO1lBQUU7UUFDcEU7SUFDRjtJQUVBLHlEQUF5RDtJQUN6RCxNQUFNaUUsZUFBZTtRQUNuQixJQUFJO2dCQTBCMEIzQjtZQXpCNUI1QyxhQUFhO1lBQ2IsTUFBTXNCLFFBQVEsTUFBTUg7WUFDcEIsTUFBTUksVUFBVXRDLHNEQUFTQTtZQUV6Qm1ELFFBQVFvQixHQUFHLENBQUM7WUFFWixNQUFNZCxXQUFXLE1BQU1qQixNQUFNLEdBQVcsT0FBUkYsU0FBUSw4Q0FBNEM7Z0JBQ2xGb0IsUUFBUTtnQkFDUmpCLFNBQVM7b0JBQ1AsaUJBQWlCLFVBQWdCLE9BQU5KO29CQUMzQixnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxJQUFJLENBQUNvQixTQUFTZixFQUFFLEVBQUU7Z0JBQ2hCLE1BQU02QyxZQUFZLE1BQU05QixTQUFTK0IsSUFBSTtnQkFDckNyQyxRQUFRM0IsS0FBSyxDQUFDLHdDQUF3QytEO2dCQUN0RCxNQUFNLElBQUk1QyxNQUFNLDhCQUFpRGMsT0FBbkJBLFNBQVN3QixNQUFNLEVBQUMsS0FBdUIsT0FBcEJ4QixTQUFTZ0MsVUFBVTtZQUN0RjtZQUVBLE1BQU05QixTQUFTLE1BQU1GLFNBQVNaLElBQUk7WUFDbENNLFFBQVFvQixHQUFHLENBQUMsb0NBQW9DWjtZQUVoRHhCLE1BQU07Z0JBQ0p5QixPQUFPO2dCQUNQQyxhQUFhLGFBQXVELE9BQTFDRixFQUFBQSxnQkFBQUEsT0FBTytCLEtBQUssY0FBWi9CLG9DQUFBQSxjQUFjZ0Msc0JBQXNCLEtBQUksR0FBRTtnQkFDcEU3QixTQUFTO1lBQ1g7WUFFQSw2QkFBNkI7WUFDN0IxQjtRQUNGLEVBQUUsT0FBT2MsS0FBYztZQUNyQkMsUUFBUTNCLEtBQUssQ0FBQyxnREFBZ0QwQjtZQUU5RGYsTUFBTTtnQkFDSnlCLE9BQU87Z0JBQ1BDLGFBQWFYLGVBQWVQLFFBQVFPLElBQUlFLE9BQU8sR0FBRztnQkFDbERVLFNBQVM7WUFDWDtRQUNGLFNBQVU7WUFDUi9DLGFBQWE7UUFDZjtJQUNGO0lBRUEsK0NBQStDO0lBQy9DLE1BQU02RSx1QkFBdUIsQ0FBQ3ZCO1FBQzVCcEMsaUJBQWlCb0M7UUFDakJ0QyxxQkFBcUI7SUFDdkI7SUFFQSx3RkFBd0Y7SUFDeEYsTUFBTThELHNCQUFzQjtRQUMxQixJQUFJLENBQUM3RCxlQUFlO1FBRXBCLElBQUk7WUFDRixtQkFBbUI7WUFDbkJELHFCQUFxQjtZQUVyQixpQ0FBaUM7WUFDakNSLGdCQUFnQmdDLENBQUFBLE9BQVM7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRSxDQUFDdkIsY0FBY3NDLEVBQUUsQ0FBQyxFQUFFO2dCQUFLO1lBRTdELE1BQU1qQyxRQUFRLE1BQU1IO1lBQ3BCLE1BQU1JLFVBQVV0QyxzREFBU0E7WUFFekIsbURBQW1EO1lBQ25ELE1BQU0wRSxjQUFjO2dCQUNsQkMsU0FBUzNDLGNBQWNzQyxFQUFFO2dCQUN6Qk0sU0FBUzVDLGNBQWM2QyxjQUFjLENBQUMsbURBQW1EO1lBQzNGO1lBRUEsTUFBTXBCLFdBQVcsTUFBTWpCLE1BQU0sR0FBVyxPQUFSRixTQUFRLHFDQUFtQztnQkFDekVvQixRQUFRO2dCQUNSakIsU0FBUztvQkFDUCxpQkFBaUIsVUFBZ0IsT0FBTko7b0JBQzNCLGdCQUFnQjtnQkFDbEI7Z0JBQ0F5QyxNQUFNQyxLQUFLQyxTQUFTLENBQUNOO1lBQ3ZCO1lBRUEsSUFBSSxDQUFDakIsU0FBU2YsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU0sOENBQWlFLE9BQW5CWCxjQUFjeUMsSUFBSTtZQUNsRjtZQUVBLE1BQU1kLFNBQVMsTUFBTUYsU0FBU1osSUFBSTtZQUVsQ1YsTUFBTTtnQkFDSnlCLE9BQU87Z0JBQ1BDLGFBQWEsaUJBQVU3QiwwQkFBQUEsb0NBQUFBLGNBQWV5QyxJQUFJLEVBQUMsd0JBQXNELE9BQWhDekMsMEJBQUFBLG9DQUFBQSxjQUFlOEQsZ0JBQWdCLEVBQUM7Z0JBQ2pHaEMsU0FBUztZQUNYO1lBRUEsb0NBQW9DO1lBQ3BDMUI7UUFDRixFQUFFLE9BQU9jLEtBQWM7WUFDckJDLFFBQVEzQixLQUFLLENBQUMseUNBQXlDMEI7WUFFdkRmLE1BQU07Z0JBQ0p5QixPQUFPO2dCQUNQQyxhQUFhWCxlQUFlUCxRQUFRTyxJQUFJRSxPQUFPLEdBQUcsOENBQWtFLE9BQXBCcEIsMEJBQUFBLG9DQUFBQSxjQUFleUMsSUFBSTtnQkFDbkhYLFNBQVM7WUFDWDtRQUNGLFNBQVU7WUFDUix3Q0FBd0M7WUFDeEMsSUFBSTlCLGVBQWU7Z0JBQ2pCVCxnQkFBZ0JnQyxDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUUsQ0FBQ3ZCLGNBQWNzQyxFQUFFLENBQUMsRUFBRTtvQkFBTTtZQUNoRTtZQUNBLDJCQUEyQjtZQUMzQnJDLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsb0VBQW9FO0lBQ3BFLCtEQUErRDtJQUMvRCxlQUFlOEQsMkJBQTJCcEIsT0FBZSxFQUFFcUIsWUFBb0I7UUFDN0UsSUFBSTtZQUNGLE1BQU0zRCxRQUFRLE1BQU1IO1lBQ3BCLE1BQU1JLFVBQVV0QyxzREFBU0E7WUFFekIsTUFBTXlELFdBQVcsTUFBTWpCLE1BQU0sR0FBVyxPQUFSRixTQUFRLHFDQUFtQztnQkFDekVvQixRQUFRO2dCQUNSakIsU0FBUztvQkFDUCxpQkFBaUIsVUFBZ0IsT0FBTko7b0JBQzNCLGdCQUFnQjtnQkFDbEI7Z0JBQ0F5QyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CTDtvQkFDQUMsU0FBU29CO2dCQUNYO1lBQ0Y7WUFFQSxJQUFJLENBQUN2QyxTQUFTZixFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLE1BQU1nQixTQUFTLE1BQU1GLFNBQVNaLElBQUk7WUFFbENWLE1BQU07Z0JBQ0p5QixPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7WUFFQSxlQUFlO1lBQ2YxQjtRQUNGLEVBQUUsT0FBT2MsS0FBYztZQUNyQkMsUUFBUTNCLEtBQUssQ0FBQyx5Q0FBeUMwQjtZQUV2RGYsTUFBTTtnQkFDSnlCLE9BQU87Z0JBQ1BDLGFBQWFYLGVBQWVQLFFBQVFPLElBQUlFLE9BQU8sR0FBRztnQkFDbERVLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSwwQ0FBMEM7SUFDMUMsTUFBTW1DLG1CQUFtQjtRQUN2QixJQUFJLENBQUMvRixRQUFRLE9BQU87WUFBRWtELFNBQVM7WUFBYzZCLFFBQVE7UUFBVTtRQUUvRCxnRkFBZ0Y7UUFDaEYsTUFBTWlCLDBCQUEwQjlGLGNBQWM4RCxNQUFNLEdBQUcsS0FBSzVELGlCQUFpQjRELE1BQU0sR0FBRztRQUV0RixvRUFBb0U7UUFDcEUsdUVBQXVFO1FBQ3ZFLElBQUksQ0FBQ2dDLHlCQUF5QjtZQUM1QixPQUFPO2dCQUNMOUMsU0FBU2xELE9BQU9pRyxhQUFhLEtBQUssSUFDOUIsd0NBQ0EsR0FBd0IsT0FBckJqRyxPQUFPaUcsYUFBYSxFQUFDO2dCQUM1QmxCLFFBQVE7WUFDVjtRQUNGO1FBRUEscUZBQXFGO1FBQ3JGLE9BQU87WUFDTDdCLFNBQVMsR0FBd0IsT0FBckJsRCxPQUFPaUcsYUFBYSxFQUFDO1lBQ2pDbEIsUUFBUTtRQUNWO0lBQ0Y7SUFFQSxpREFBaUQ7SUFDakQsTUFBTW1CLG9CQUFvQixDQUFDbkI7UUFDekIsT0FBUUE7WUFDTixLQUFLO2dCQUFRLE9BQU87WUFDcEIsS0FBSztnQkFBVyxPQUFPO1lBQ3ZCLEtBQUs7Z0JBQU8sT0FBTztZQUNuQjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNb0IsZ0JBQWdCSjtJQUV0QiwwQkFBMEI7SUFDMUIsTUFBTUssYUFBYSxDQUFDQztRQUNsQixJQUFJLENBQUNBLFlBQVksT0FBTztRQUV4QixNQUFNQyxPQUFPLElBQUlDLEtBQUtGO1FBQ3RCLE9BQU9DLEtBQUtFLGtCQUFrQixDQUFDLFNBQVM7WUFDdENDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxLQUFLO1FBQ1A7SUFDRjtJQUVBLHFCQUNFLDhEQUFDNUkscURBQUlBOzswQkFDSCw4REFBQ0UsMkRBQVVBO2dCQUFDMkksV0FBVTs7a0NBQ3BCLDhEQUFDekksa0tBQVFBO3dCQUFDeUksV0FBVyxnQkFBd0QsT0FBeENWLGtCQUFrQkMsY0FBY3BCLE1BQU07Ozs7OztrQ0FDM0UsOERBQUM4QjtrQ0FDQyw0RUFBQzNJLDBEQUFTQTs0QkFBQzBJLFdBQVU7c0NBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUtuQyw4REFBQzVJLDREQUFXQTs7b0JBQ1RzQywwQkFDQyw4REFBQ3VHO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ3hJLGtLQUFPQTtnQ0FBQ3dJLFdBQVU7Ozs7OzswQ0FDbkIsOERBQUNFO2dDQUFFRixXQUFVOzBDQUFxQzs7Ozs7Ozs7Ozs7K0JBRWxEdEYsc0JBQ0YsOERBQUN1Rjt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUN2SSxrS0FBV0E7Z0NBQUN1SSxXQUFVOzs7Ozs7MENBQ3ZCLDhEQUFDRTtnQ0FBRUYsV0FBVTswQ0FBd0J0Rjs7Ozs7Ozs7Ozs7NkNBR3ZDO2tDQUNFLDRFQUFDdUY7NEJBQUlELFdBQVU7OzhDQUViLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUN0SSxrS0FBUUE7NENBQUNzSSxXQUFVOzs7Ozs7c0RBQ3BCLDhEQUFDRTs0Q0FBRUYsV0FBVTtzREFBV1QsY0FBY2pELE9BQU87Ozs7Ozs7Ozs7Ozs4Q0FtQy9DLDhEQUFDMkQ7b0NBQUlELFdBQVU7O3NEQUViLDhEQUFDNUgseURBQU1BOzRDQUNMK0gsU0FBU2xEOzRDQUNUbUQsVUFBVXRHLGVBQWVFLGFBQWFWLGNBQWM4RCxNQUFNLEtBQUs7NENBQy9ENEMsV0FBVTs0Q0FDVmhELFNBQVE7c0RBRVBsRCw0QkFDQzs7a0VBQ0UsOERBQUN0QyxrS0FBT0E7d0RBQUN3SSxXQUFVOzs7Ozs7b0RBQThCO29EQUN6QzlGLGlCQUFpQkUsT0FBTztvREFBQztvREFBRUYsaUJBQWlCRyxLQUFLO29EQUFDOzs2RUFHNUQ7O2tFQUNFLDhEQUFDdkMsa0tBQU1BO3dEQUFDa0ksV0FBVTs7Ozs7O29EQUFpQjs7Ozs7Ozs7c0RBT3pDLDhEQUFDNUgseURBQU1BOzRDQUNMK0gsU0FBUzNCOzRDQUNUNEIsVUFBVXRHLGVBQWVFOzRDQUN6QmdHLFdBQVU7NENBQ1ZoRCxTQUFRO3NEQUVQaEQsMEJBQ0M7O2tFQUNFLDhEQUFDeEMsa0tBQU9BO3dEQUFDd0ksV0FBVTs7Ozs7O29EQUE4Qjs7NkVBSW5EOztrRUFDRSw4REFBQ25JLGtLQUFLQTt3REFBQ21JLFdBQVU7Ozs7OztvREFBaUI7Ozs7Ozs7O3NEQU94Qyw4REFBQzVILHlEQUFNQTs0Q0FDTDRFLFNBQVE7NENBQ1JxRCxNQUFLOzRDQUNMRixTQUFTN0U7NENBQ1Q4RSxVQUFVMUcsYUFBYUksZUFBZUU7NENBQ3RDOEMsT0FBTTtzREFFTiw0RUFBQy9FLGtLQUFTQTtnREFBQ2lJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQUt4QmxHLDZCQUNDLDhEQUFDbUc7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNNOzhEQUFLOzs7Ozs7OERBQ04sOERBQUNBOzt3REFBTXBHLGlCQUFpQkUsT0FBTzt3REFBQzt3REFBRUYsaUJBQWlCRyxLQUFLO3dEQUFDOzs7Ozs7Ozs7Ozs7O3NEQUUzRCw4REFBQzRGOzRDQUFJRCxXQUFVO3NEQUNiLDRFQUFDQztnREFDQ0QsV0FBVTtnREFDVk8sT0FBTztvREFBRUMsT0FBTyxHQUEwRSxPQUF2RSxpQkFBa0JwRyxPQUFPLEdBQUdxRyxLQUFLQyxHQUFHLENBQUN4RyxpQkFBaUJHLEtBQUssRUFBRSxLQUFNLEtBQUk7Z0RBQUc7Ozs7Ozs7Ozs7O3NEQUdqRyw4REFBQzRGOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ007b0RBQUtOLFdBQVU7O3dEQUFrQjlGLGlCQUFpQkksT0FBTzt3REFBQzs7Ozs7Ozs4REFDM0QsOERBQUNnRztvREFBS04sV0FBVTs7d0RBQWdCOUYsaUJBQWlCSyxNQUFNO3dEQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU05RCw4REFBQzBGO29DQUFJRCxXQUFVOztzREFFYiw4REFBQy9ILG1FQUFXQTs0Q0FDVjBJLE1BQU0vRixTQUFTRSxPQUFPOzRDQUN0QjhGLGNBQWMsSUFBTXJFLGVBQWU7NENBQ25DeUQsV0FBVTs7OERBRVYsOERBQUM3SCwwRUFBa0JBO29EQUFDNkgsV0FBVTs7c0VBQzVCLDhEQUFDQzs0REFBSUQsV0FBVTs7OEVBQ2IsOERBQUN0SSxrS0FBUUE7b0VBQUNzSSxXQUFVOzs7Ozs7OEVBQ3BCLDhEQUFDTTs7d0VBQUs7d0VBQXFDaEgsY0FBYzhELE1BQU07d0VBQUM7Ozs7Ozs7Ozs7Ozs7d0RBRWpFeEMsU0FBU0UsT0FBTyxpQkFDZiw4REFBQ25ELGtLQUFXQTs0REFBQ3FJLFdBQVU7Ozs7O2lGQUV2Qiw4REFBQ3BJLGtLQUFZQTs0REFBQ29JLFdBQVU7Ozs7Ozs7Ozs7Ozs4REFJNUIsOERBQUM5SCwwRUFBa0JBO29EQUFDOEgsV0FBVTs4REFDM0IxRyxjQUFjOEQsTUFBTSxLQUFLLGtCQUN4Qiw4REFBQzhDO3dEQUFFRixXQUFVO2tFQUFxQzs7Ozs7NkVBRWxELDhEQUFDckgsdURBQUtBOzswRUFDSiw4REFBQ0ksNkRBQVdBOzBFQUNWLDRFQUFDQywwREFBUUE7O3NGQUNQLDhEQUFDRiwyREFBU0E7c0ZBQUM7Ozs7OztzRkFDWCw4REFBQ0EsMkRBQVNBO3NGQUFDOzs7Ozs7c0ZBQ1gsOERBQUNBLDJEQUFTQTtzRkFBQzs7Ozs7O3NGQUNYLDhEQUFDQSwyREFBU0E7c0ZBQUM7Ozs7OztzRkFDWCw4REFBQ0EsMkRBQVNBO3NGQUFDOzs7Ozs7Ozs7Ozs7Ozs7OzswRUFHZiw4REFBQ0YsMkRBQVNBOzBFQUNQVSxjQUFjdUgsR0FBRyxDQUFDdEQsQ0FBQUEsc0JBQ2pCLDhEQUFDdkUsMERBQVFBOzswRkFDUCw4REFBQ0gsMkRBQVNBO2dGQUFDbUgsV0FBVTswRkFBZXpDLE1BQU1JLElBQUk7Ozs7OzswRkFDOUMsOERBQUM5RSwyREFBU0E7MEZBQUUyRyxXQUFXakMsTUFBTW1DLElBQUk7Ozs7OzswRkFDakMsOERBQUM3RywyREFBU0E7MEZBQUUwRSxNQUFNeUIsZ0JBQWdCOzs7Ozs7MEZBQ2xDLDhEQUFDbkcsMkRBQVNBO2dGQUFDbUgsV0FBVTswRkFBZ0J6QyxNQUFNdUQsbUJBQW1CLElBQUk7Ozs7OzswRkFDbEUsOERBQUNqSSwyREFBU0E7MEZBQ1IsNEVBQUNULHlEQUFNQTtvRkFDTDRFLFNBQVE7b0ZBQ1JxRCxNQUFLO29GQUNMRixTQUFTLElBQU1yQixxQkFBcUJ2QjtvRkFDcEM2QyxVQUFVNUYsWUFBWSxDQUFDK0MsTUFBTUMsRUFBRSxDQUFDOzhGQUUvQmhELFlBQVksQ0FBQytDLE1BQU1DLEVBQUUsQ0FBQyxpQkFDckI7OzBHQUNFLDhEQUFDaEcsa0tBQU9BO2dHQUFDd0ksV0FBVTs7Ozs7OzRGQUE4Qjs7dUdBSW5EOzs7Ozs7Ozs7Ozs7dUVBbEJPekMsTUFBTUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQStCbkMsOERBQUN2RixtRUFBV0E7NENBQ1YwSSxNQUFNL0YsU0FBU0csVUFBVTs0Q0FDekI2RixjQUFjLElBQU1yRSxlQUFlOzRDQUNuQ3lELFdBQVU7OzhEQUVWLDhEQUFDN0gsMEVBQWtCQTtvREFBQzZILFdBQVU7O3NFQUM1Qiw4REFBQ0M7NERBQUlELFdBQVU7OzhFQUNiLDhEQUFDdEksa0tBQVFBO29FQUFDc0ksV0FBVTs7Ozs7OzhFQUNwQiw4REFBQ007O3dFQUFLO3dFQUErQjlHLGlCQUFpQjRELE1BQU07d0VBQUM7Ozs7Ozs7Ozs7Ozs7d0RBRTlEeEMsU0FBU0csVUFBVSxpQkFDbEIsOERBQUNwRCxrS0FBV0E7NERBQUNxSSxXQUFVOzs7OztpRkFFdkIsOERBQUNwSSxrS0FBWUE7NERBQUNvSSxXQUFVOzs7Ozs7Ozs7Ozs7OERBSTVCLDhEQUFDOUgsMEVBQWtCQTtvREFBQzhILFdBQVU7OERBQzNCeEcsaUJBQWlCNEQsTUFBTSxLQUFLLGtCQUMzQiw4REFBQzhDO3dEQUFFRixXQUFVO2tFQUFxQzs7Ozs7NkVBRWxELDhEQUFDckgsdURBQUtBOzswRUFDSiw4REFBQ0ksNkRBQVdBOzBFQUNWLDRFQUFDQywwREFBUUE7O3NGQUNQLDhEQUFDRiwyREFBU0E7c0ZBQUM7Ozs7OztzRkFDWCw4REFBQ0EsMkRBQVNBO3NGQUFDOzs7Ozs7c0ZBQ1gsOERBQUNBLDJEQUFTQTtzRkFBQzs7Ozs7O3NGQUNYLDhEQUFDQSwyREFBU0E7c0ZBQUM7Ozs7OztzRkFDWCw4REFBQ0EsMkRBQVNBO3NGQUFDOzs7Ozs7Ozs7Ozs7Ozs7OzswRUFHZiw4REFBQ0YsMkRBQVNBOzBFQUNQWSxpQkFBaUJxSCxHQUFHLENBQUN0RCxDQUFBQSxzQkFDcEIsOERBQUN2RSwwREFBUUE7OzBGQUNQLDhEQUFDSCwyREFBU0E7Z0ZBQUNtSCxXQUFVOzBGQUFlekMsTUFBTUksSUFBSTs7Ozs7OzBGQUM5Qyw4REFBQzlFLDJEQUFTQTswRkFBRTJHLFdBQVdqQyxNQUFNbUMsSUFBSTs7Ozs7OzBGQUNqQyw4REFBQzdHLDJEQUFTQTswRkFBRTBFLE1BQU15QixnQkFBZ0I7Ozs7OzswRkFDbEMsOERBQUNuRywyREFBU0E7Z0ZBQUNtSCxXQUFVOzBGQUFtQnpDLE1BQU11RCxtQkFBbUI7Ozs7OzswRkFDakUsOERBQUNqSSwyREFBU0E7MEZBQ1IsNEVBQUNULHlEQUFNQTtvRkFDTDRFLFNBQVE7b0ZBQ1JxRCxNQUFLO29GQUNMRixTQUFTLElBQU1yQixxQkFBcUJ2QjtvRkFDcEM2QyxVQUFVNUYsWUFBWSxDQUFDK0MsTUFBTUMsRUFBRSxDQUFDOzhGQUUvQmhELFlBQVksQ0FBQytDLE1BQU1DLEVBQUUsQ0FBQyxpQkFDckI7OzBHQUNFLDhEQUFDaEcsa0tBQU9BO2dHQUFDd0ksV0FBVTs7Ozs7OzRGQUE4Qjs7dUdBSW5EOzs7Ozs7Ozs7Ozs7dUVBbEJPekMsTUFBTUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FtQzdDLDhEQUFDbkYseURBQU1BO3dCQUFDc0ksTUFBTTNGO3dCQUFtQjRGLGNBQWMzRjtrQ0FDN0MsNEVBQUMzQyxnRUFBYUE7OzhDQUNaLDhEQUFDRywrREFBWUE7O3NEQUNYLDhEQUFDQyw4REFBV0E7c0RBQUM7Ozs7OztzREFDYiw4REFBQ0gsb0VBQWlCQTtzREFDZjJDLCtCQUNDLDhEQUFDK0U7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDRTtrRUFBRTs7Ozs7O2tFQUVILDhEQUFDRDt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUNFOztrRkFBRSw4REFBQ2E7a0ZBQU87Ozs7OztvRUFBZTtvRUFBRTdGLDBCQUFBQSxvQ0FBQUEsY0FBZXlDLElBQUk7Ozs7Ozs7MEVBQy9DLDhEQUFDdUM7O2tGQUFFLDhEQUFDYTtrRkFBTzs7Ozs7O29FQUFjO29FQUFFdkIsV0FBV3RFLENBQUFBLDBCQUFBQSxvQ0FBQUEsY0FBZXdFLElBQUksS0FBSTs7Ozs7OzswRUFDN0QsOERBQUNROztrRkFBRSw4REFBQ2E7a0ZBQU87Ozs7OztvRUFBdUI7b0VBQUU3RixDQUFBQSwwQkFBQUEsb0NBQUFBLGNBQWU0RixtQkFBbUIsS0FBSTs7Ozs7OzswRUFDMUUsOERBQUNaOztrRkFBRSw4REFBQ2E7a0ZBQU87Ozs7OztvRUFBc0I7b0VBQUU3RiwwQkFBQUEsb0NBQUFBLGNBQWU4RCxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7a0VBR3BFLDhEQUFDa0I7a0VBQUU7Ozs7OztrRUFDSCw4REFBQ2M7d0RBQUdoQixXQUFVOzswRUFDWiw4REFBQ2lCOzBFQUFHOzs7Ozs7MEVBQ0osOERBQUNBOzBFQUFHOzs7Ozs7MEVBQ0osOERBQUNBOzBFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNZCw4REFBQ3pJLCtEQUFZQTs7c0RBQ1gsOERBQUNKLHlEQUFNQTs0Q0FBQzRFLFNBQVE7NENBQVVtRCxTQUFTLElBQU1sRixxQkFBcUI7c0RBQVE7Ozs7OztzREFDdEUsOERBQUM3Qyx5REFBTUE7NENBQUMrSCxTQUFTcEI7c0RBQXFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9sRDtHQW51QmdCNUY7O1FBaUJhbkIsb0RBQU9BO1FBQ2hCaUIsc0RBQVFBOzs7S0FsQlpFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJveGFzXFxPbmVEcml2ZVxcRGVza3RvcFxcUFJPSkVDVFNcXHR1Y3NvbmxvdmVzbXVzaWMuY29tXFxQcm90b3R5cGUxM1xcZnJvbnRlbmRcXGNvbXBvbmVudHNcXGFkbWluXFxldmVudC1yZWxhdGlvbnNcXHZlbnVlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiO1xuaW1wb3J0IHtcbiAgR2l0TWVyZ2UsXG4gIExvYWRlcjIsXG4gIEFsZXJ0Q2lyY2xlLFxuICBDYWxlbmRhcixcbiAgQ2hldnJvbkRvd24sXG4gIENoZXZyb25SaWdodCxcbiAgQ2hlY2ssXG4gIFdyZW5jaCxcbiAgUmVmcmVzaEN3XG59IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tIFwiQC9ob29rcy91c2UtYXV0aFwiO1xuaW1wb3J0IHsgXG4gIENvbGxhcHNpYmxlLFxuICBDb2xsYXBzaWJsZUNvbnRlbnQsXG4gIENvbGxhcHNpYmxlVHJpZ2dlcixcbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9jb2xsYXBzaWJsZVwiO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcbmltcG9ydCB7XG4gIERpYWxvZyxcbiAgRGlhbG9nQ29udGVudCxcbiAgRGlhbG9nRGVzY3JpcHRpb24sXG4gIERpYWxvZ0Zvb3RlcixcbiAgRGlhbG9nSGVhZGVyLFxuICBEaWFsb2dUaXRsZSxcbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9kaWFsb2dcIjtcbmltcG9ydCB7XG4gIFRhYmxlLFxuICBUYWJsZUJvZHksXG4gIFRhYmxlQ2VsbCxcbiAgVGFibGVIZWFkLFxuICBUYWJsZUhlYWRlcixcbiAgVGFibGVSb3csXG59IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdGFibGVcIjtcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSBcIkAvaG9va3MvdXNlLXRvYXN0XCI7XG5pbXBvcnQgeyBnZXRBcGlVcmwgfSBmcm9tIFwiQC9saWIvY29uZmlnXCI7XG5cbmludGVyZmFjZSBFdmVudE1pc3NpbmdWZW51ZSB7XG4gIGlkOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgZGF0ZTogc3RyaW5nO1xuICBhenVyZV92ZW51ZV9pZDogc3RyaW5nO1xuICBhenVyZV92ZW51ZV9uYW1lOiBzdHJpbmc7XG4gIHBvc3RncmVzX3ZlbnVlX2lkOiBzdHJpbmcgfCBudWxsO1xuICBwb3N0Z3Jlc192ZW51ZV9uYW1lOiBzdHJpbmcgfCBudWxsO1xufVxuXG5pbnRlcmZhY2UgRXZlbnRWZW51ZVN0YXR1c0NvdW50cyB7XG4gIHRvdGFsOiBudW1iZXI7XG4gIG1pc3NpbmdWZW51ZXM6IG51bWJlcjtcbiAgcG90ZW50aWFsRml4ZXM6IG51bWJlcjtcbiAgbWlzbWF0Y2hlZFZlbnVlczogbnVtYmVyO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gRXZlbnRWZW51ZVJlbGF0aW9ucygpIHtcbiAgY29uc3QgW2NvdW50cywgc2V0Q291bnRzXSA9IHVzZVN0YXRlPEV2ZW50VmVudWVTdGF0dXNDb3VudHMgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW21pc3NpbmdFdmVudHMsIHNldE1pc3NpbmdFdmVudHNdID0gdXNlU3RhdGU8RXZlbnRNaXNzaW5nVmVudWVbXT4oW10pO1xuICBjb25zdCBbbWlzbWF0Y2hlZEV2ZW50cywgc2V0TWlzbWF0Y2hlZEV2ZW50c10gPSB1c2VTdGF0ZTxFdmVudE1pc3NpbmdWZW51ZVtdPihbXSk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2lzQnVsa0ZpeGluZywgc2V0SXNCdWxrRml4aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2JhdGNoRml4aW5nLCBzZXRCYXRjaEZpeGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtjc3ZGaXhpbmcsIHNldENzdkZpeGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtiYXRjaEZpeFByb2dyZXNzLCBzZXRCYXRjaEZpeFByb2dyZXNzXSA9IHVzZVN0YXRlKHsgY3VycmVudDogMCwgdG90YWw6IDAsIHN1Y2Nlc3M6IDAsIGZhaWxlZDogMCB9KTtcbiAgY29uc3QgW2ZpeGluZ0V2ZW50cywgc2V0Rml4aW5nRXZlbnRzXSA9IHVzZVN0YXRlPFJlY29yZDxzdHJpbmcsIGJvb2xlYW4+Pih7fSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtleHBhbmRlZCwgc2V0RXhwYW5kZWRdID0gdXNlU3RhdGU8eyBtaXNzaW5nOiBib29sZWFuOyBtaXNtYXRjaGVkOiBib29sZWFuIH0+KHtcbiAgICBtaXNzaW5nOiBmYWxzZSxcbiAgICBtaXNtYXRjaGVkOiBmYWxzZVxuICB9KTtcbiAgY29uc3QgW2NvbmZpcm1EaWFsb2dPcGVuLCBzZXRDb25maXJtRGlhbG9nT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzZWxlY3RlZEV2ZW50LCBzZXRTZWxlY3RlZEV2ZW50XSA9IHVzZVN0YXRlPEV2ZW50TWlzc2luZ1ZlbnVlIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IHsgZ2V0QWNjZXNzVG9rZW4gfSA9IHVzZUF1dGgoKTtcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKTtcblxuICAvLyBGZXRjaCBkYXRhIG9uIGNvbXBvbmVudCBtb3VudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZldGNoRGF0YSgpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgZmV0Y2hEYXRhID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCB0b2tlbiA9IGF3YWl0IGdldEFjY2Vzc1Rva2VuKCk7XG4gICAgICBjb25zdCBiYXNlVXJsID0gZ2V0QXBpVXJsKCk7XG4gICAgICBcbiAgICAgIC8vIEZldGNoIGNvdW50c1xuICAgICAgY29uc3QgY291bnRzUmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtiYXNlVXJsfS9hZG1pbi9ldmVudC12ZW51ZS1yZWxhdGlvbnNgLCB7XG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gXG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIWNvdW50c1Jlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIGV2ZW50LXZlbnVlIHJlbGF0aW9uc2hpcCBjb3VudHMnKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgY291bnRzRGF0YSA9IGF3YWl0IGNvdW50c1Jlc3BvbnNlLmpzb24oKTtcbiAgICAgIHNldENvdW50cyhjb3VudHNEYXRhKTtcblxuICAgICAgLy8gRmV0Y2ggbWlzc2luZyBldmVudHNcbiAgICAgIGNvbnN0IG1pc3NpbmdSZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2Jhc2VVcmx9L2FkbWluL2V2ZW50LXZlbnVlLXJlbGF0aW9ucy9taXNzaW5nYCwge1xuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgICB9XG4gICAgICB9KTtcblxuICAgICAgaWYgKG1pc3NpbmdSZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBtaXNzaW5nRGF0YSA9IGF3YWl0IG1pc3NpbmdSZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHNldE1pc3NpbmdFdmVudHMobWlzc2luZ0RhdGEpO1xuICAgICAgfVxuXG4gICAgICAvLyBGZXRjaCBtaXNtYXRjaGVkIGV2ZW50c1xuICAgICAgY29uc3QgbWlzbWF0Y2hlZFJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YmFzZVVybH0vYWRtaW4vZXZlbnQtdmVudWUtcmVsYXRpb25zL21pc21hdGNoZWRgLCB7XG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gXG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgICAgXG4gICAgICBpZiAobWlzbWF0Y2hlZFJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IG1pc21hdGNoZWREYXRhID0gYXdhaXQgbWlzbWF0Y2hlZFJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgc2V0TWlzbWF0Y2hlZEV2ZW50cyhtaXNtYXRjaGVkRGF0YSk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIHNldEVycm9yKG51bGwpO1xuICAgIH0gY2F0Y2ggKGVycjogdW5rbm93bikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgZXZlbnQtdmVudWUgcmVsYXRpb25zaGlwIGRhdGEnLCBlcnIpO1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gZmV0Y2ggZXZlbnQtdmVudWUgcmVsYXRpb25zaGlwIGRhdGEnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgdG9nZ2xlRXhwYW5kZWQgPSAoc2VjdGlvbjogJ21pc3NpbmcnIHwgJ21pc21hdGNoZWQnKSA9PiB7XG4gICAgc2V0RXhwYW5kZWQocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtzZWN0aW9uXTogIXByZXZbc2VjdGlvbl1cbiAgICB9KSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRml4QWxsID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRJc0J1bGtGaXhpbmcodHJ1ZSk7XG4gICAgICBjb25zdCB0b2tlbiA9IGF3YWl0IGdldEFjY2Vzc1Rva2VuKCk7XG4gICAgICBjb25zdCBiYXNlVXJsID0gZ2V0QXBpVXJsKCk7XG4gICAgICBcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YmFzZVVybH0vYWRtaW4vZXZlbnQtdmVudWUtcmVsYXRpb25zL2ZpeC1hbGxgLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgICAgXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZpeCBhbGwgZXZlbnQtdmVudWUgcmVsYXRpb25zaGlwcycpO1xuICAgICAgfVxuICAgICAgXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6ICdTdWNjZXNzJyxcbiAgICAgICAgZGVzY3JpcHRpb246IHJlc3VsdC5tZXNzYWdlLFxuICAgICAgICB2YXJpYW50OiAnZGVmYXVsdCdcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICAvLyBSZWZyZXNoIGRhdGFcbiAgICAgIGZldGNoRGF0YSgpO1xuICAgIH0gY2F0Y2ggKGVycjogdW5rbm93bikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZml4aW5nIGFsbCBldmVudC12ZW51ZSByZWxhdGlvbnNoaXBzJywgZXJyKTtcbiAgICAgIFxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ0Vycm9yJyxcbiAgICAgICAgZGVzY3JpcHRpb246IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGZpeCBhbGwgZXZlbnQtdmVudWUgcmVsYXRpb25zaGlwcycsXG4gICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0J1bGtGaXhpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBIYW5kbGUgYmF0Y2ggZml4aW5nIGEgbGltaXRlZCBudW1iZXIgb2YgZXZlbnRzXG4gIGNvbnN0IGhhbmRsZUJhdGNoRml4ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRCYXRjaEZpeGluZyh0cnVlKTtcbiAgICAgIGNvbnN0IHRva2VuID0gYXdhaXQgZ2V0QWNjZXNzVG9rZW4oKTtcbiAgICAgIGNvbnN0IGJhc2VVcmwgPSBnZXRBcGlVcmwoKTtcbiAgICAgIFxuICAgICAgLy8gRGV0ZXJtaW5lIHdoaWNoIGV2ZW50cyB0byBmaXggLSBwcmlvcml0aXplIG1pc3NpbmcgZXZlbnRzXG4gICAgICBjb25zdCBldmVudHNUb0ZpeCA9IFsuLi5taXNzaW5nRXZlbnRzXS5zbGljZSgwLCAxMCk7IC8vIExpbWl0IHRvIDEwIGV2ZW50cyBhdCBhIHRpbWVcbiAgICAgIFxuICAgICAgaWYgKGV2ZW50c1RvRml4Lmxlbmd0aCA9PT0gMCkge1xuICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgdGl0bGU6ICdObyBFdmVudHMgdG8gRml4JyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ1RoZXJlIGFyZSBubyBldmVudHMgdGhhdCBuZWVkIGZpeGluZy4nLFxuICAgICAgICAgIHZhcmlhbnQ6ICdkZWZhdWx0J1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBTZXQgdXAgcHJvZ3Jlc3MgdHJhY2tpbmdcbiAgICAgIHNldEJhdGNoRml4UHJvZ3Jlc3Moe1xuICAgICAgICBjdXJyZW50OiAwLFxuICAgICAgICB0b3RhbDogZXZlbnRzVG9GaXgubGVuZ3RoLFxuICAgICAgICBzdWNjZXNzOiAwLFxuICAgICAgICBmYWlsZWQ6IDBcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICAvLyBNYXJrIGFsbCBldmVudHMgYXMgYmVpbmcgZml4ZWRcbiAgICAgIGNvbnN0IG5ld0ZpeGluZ1N0YXRlOiBSZWNvcmQ8c3RyaW5nLCBib29sZWFuPiA9IHt9O1xuICAgICAgZXZlbnRzVG9GaXguZm9yRWFjaChldmVudCA9PiB7XG4gICAgICAgIG5ld0ZpeGluZ1N0YXRlW2V2ZW50LmlkXSA9IHRydWU7XG4gICAgICB9KTtcbiAgICAgIHNldEZpeGluZ0V2ZW50cyhwcmV2ID0+ICh7IC4uLnByZXYsIC4uLm5ld0ZpeGluZ1N0YXRlIH0pKTtcbiAgICAgIFxuICAgICAgY29uc29sZS5sb2coYPCflKcgW0Zyb250ZW5kXSBTdGFydGluZyBiYXRjaCBmaXggZm9yICR7ZXZlbnRzVG9GaXgubGVuZ3RofSB2ZW51ZSByZWxhdGlvbnNoaXBzYCk7XG4gICAgICBcbiAgICAgIC8vIFByb2Nlc3MgZXZlbnRzIHNlcXVlbnRpYWxseSB0byBhdm9pZCBvdmVyd2hlbG1pbmcgdGhlIHNlcnZlclxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBldmVudHNUb0ZpeC5sZW5ndGg7IGkrKykge1xuICAgICAgICBjb25zdCBldmVudCA9IGV2ZW50c1RvRml4W2ldO1xuICAgICAgICBzZXRCYXRjaEZpeFByb2dyZXNzKHByZXYgPT4gKHsgLi4ucHJldiwgY3VycmVudDogaSArIDEgfSkpO1xuICAgICAgICBcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UpyBbRnJvbnRlbmRdIEZpeGluZyBldmVudCAke2kgKyAxfS8ke2V2ZW50c1RvRml4Lmxlbmd0aH06ICR7ZXZlbnQubmFtZX0gKElEOiAke2V2ZW50LmlkfSlgKTtcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBDcmVhdGUgdGhlIHJlcXVlc3QgYm9keSB3aXRoIGV2ZW50SWQgYW5kIHZlbnVlSWRcbiAgICAgICAgICBjb25zdCByZXF1ZXN0Qm9keSA9IHtcbiAgICAgICAgICAgIGV2ZW50SWQ6IGV2ZW50LmlkLFxuICAgICAgICAgICAgdmVudWVJZDogZXZlbnQuYXp1cmVfdmVudWVfaWQgLy8gVXNpbmcgdGhlIEF6dXJlIHZlbnVlIElEIGZyb20gdGhlIHNlbGVjdGVkIGV2ZW50XG4gICAgICAgICAgfTtcbiAgICAgICAgICBcbiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2Jhc2VVcmx9L2FkbWluL2V2ZW50LXZlbnVlLXJlbGF0aW9ucy9maXhgLCB7XG4gICAgICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHJlcXVlc3RCb2R5KVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIFxuICAgICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYPCflKcgW0Zyb250ZW5kXSBGYWlsZWQgdG8gZml4IGV2ZW50ICR7ZXZlbnQubmFtZX06ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgICAgICAgICAgc2V0QmF0Y2hGaXhQcm9ncmVzcyhwcmV2ID0+ICh7IC4uLnByZXYsIGZhaWxlZDogcHJldi5mYWlsZWQgKyAxIH0pKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uc29sZS5sb2coYPCflKcgW0Zyb250ZW5kXSBTdWNjZXNzZnVsbHkgZml4ZWQgZXZlbnQgJHtldmVudC5uYW1lfWApO1xuICAgICAgICAgICAgc2V0QmF0Y2hGaXhQcm9ncmVzcyhwcmV2ID0+ICh7IC4uLnByZXYsIHN1Y2Nlc3M6IHByZXYuc3VjY2VzcyArIDEgfSkpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXZlbnRFcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYPCflKcgW0Zyb250ZW5kXSBFcnJvciBmaXhpbmcgZXZlbnQgJHtldmVudC5uYW1lfTpgLCBldmVudEVycm9yKTtcbiAgICAgICAgICBzZXRCYXRjaEZpeFByb2dyZXNzKHByZXYgPT4gKHsgLi4ucHJldiwgZmFpbGVkOiBwcmV2LmZhaWxlZCArIDEgfSkpO1xuICAgICAgICB9IGZpbmFsbHkge1xuICAgICAgICAgIC8vIE1hcmsgdGhpcyBldmVudCBhcyBubyBsb25nZXIgYmVpbmcgZml4ZWRcbiAgICAgICAgICBzZXRGaXhpbmdFdmVudHMocHJldiA9PiAoeyAuLi5wcmV2LCBbZXZlbnQuaWRdOiBmYWxzZSB9KSk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC8vIFNtYWxsIGRlbGF5IHRvIGF2b2lkIG92ZXJ3aGVsbWluZyB0aGUgc2VydmVyXG4gICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCA1MDApKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ0JhdGNoIEZpeCBDb21wbGV0ZScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBgU3VjY2Vzc2Z1bGx5IGZpeGVkICR7YmF0Y2hGaXhQcm9ncmVzcy5zdWNjZXNzfSBvdXQgb2YgJHtldmVudHNUb0ZpeC5sZW5ndGh9IHZlbnVlIHJlbGF0aW9uc2hpcHMuYCxcbiAgICAgICAgdmFyaWFudDogJ2RlZmF1bHQnXG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgLy8gUmVmcmVzaCBkYXRhXG4gICAgICBmZXRjaERhdGEoKTtcbiAgICB9IGNhdGNoIChlcnI6IHVua25vd24pIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGR1cmluZyBiYXRjaCBmaXggb3BlcmF0aW9uJywgZXJyKTtcbiAgICAgIFxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ0Vycm9yJyxcbiAgICAgICAgZGVzY3JpcHRpb246IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGNvbXBsZXRlIGJhdGNoIGZpeCBvcGVyYXRpb24nLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QmF0Y2hGaXhpbmcoZmFsc2UpO1xuICAgICAgLy8gUmVzZXQgcHJvZ3Jlc3NcbiAgICAgIHNldEJhdGNoRml4UHJvZ3Jlc3MoeyBjdXJyZW50OiAwLCB0b3RhbDogMCwgc3VjY2VzczogMCwgZmFpbGVkOiAwIH0pO1xuICAgIH1cbiAgfTtcblxuICAvLyBIYW5kbGUgQ1NWLWJhc2VkIGZpeCBmb3IgYWxsIGV2ZW50LXZlbnVlIHJlbGF0aW9uc2hpcHNcbiAgY29uc3QgaGFuZGxlQ3N2Rml4ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRDc3ZGaXhpbmcodHJ1ZSk7XG4gICAgICBjb25zdCB0b2tlbiA9IGF3YWl0IGdldEFjY2Vzc1Rva2VuKCk7XG4gICAgICBjb25zdCBiYXNlVXJsID0gZ2V0QXBpVXJsKCk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5SnIFtGcm9udGVuZF0gU3RhcnRpbmcgQ1NWLWJhc2VkIGZpeCBmb3IgZXZlbnQtdmVudWUgcmVsYXRpb25zaGlwcycpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2Jhc2VVcmx9L2FkbWluL2V2ZW50LXZlbnVlLXJlbGF0aW9ucy9maXgtZnJvbS1jc3ZgLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcbiAgICAgICAgY29uc29sZS5lcnJvcign8J+UpyBbRnJvbnRlbmRdIFNlcnZlciBlcnJvciByZXNwb25zZTonLCBlcnJvclRleHQpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBleGVjdXRlIENTViBmaXg6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SnIFtGcm9udGVuZF0gQ1NWIGZpeCBjb21wbGV0ZWQ6JywgcmVzdWx0KTtcblxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ0NTViBGaXggQ29tcGxldGUnLFxuICAgICAgICBkZXNjcmlwdGlvbjogYFZhbGlkYXRlZCAke3Jlc3VsdC5zdGF0cz8udmFsaWRhdGVkUmVsYXRpb25zaGlwcyB8fCAwfSB2ZW51ZSByZWxhdGlvbnNoaXBzIGZyb20gQ1NWYCxcbiAgICAgICAgdmFyaWFudDogJ2RlZmF1bHQnXG4gICAgICB9KTtcblxuICAgICAgLy8gUmVmcmVzaCBkYXRhIGFmdGVyIENTViBmaXhcbiAgICAgIGZldGNoRGF0YSgpO1xuICAgIH0gY2F0Y2ggKGVycjogdW5rbm93bikge1xuICAgICAgY29uc29sZS5lcnJvcign8J+UpyBbRnJvbnRlbmRdIEVycm9yIGR1cmluZyBDU1YgZml4IG9wZXJhdGlvbicsIGVycik7XG5cbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6ICdFcnJvcicsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBjb21wbGV0ZSBDU1YgZml4IG9wZXJhdGlvbicsXG4gICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRDc3ZGaXhpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBPcGVuIGNvbmZpcm1hdGlvbiBkaWFsb2cgZm9yIGZpeGluZyBhbiBldmVudFxuICBjb25zdCBvcGVuRml4Q29uZmlybURpYWxvZyA9IChldmVudDogRXZlbnRNaXNzaW5nVmVudWUpID0+IHtcbiAgICBzZXRTZWxlY3RlZEV2ZW50KGV2ZW50KTtcbiAgICBzZXRDb25maXJtRGlhbG9nT3Blbih0cnVlKTtcbiAgfTtcblxuICAvLyBIYW5kbGUgZml4aW5nIGEgc2luZ2xlIGV2ZW50J3MgdmVudWUgcmVsYXRpb25zaGlwIHRocm91Z2ggdGhlIG5ldyBjb25maXJtYXRpb24gZGlhbG9nXG4gIGNvbnN0IGhhbmRsZUZpeEV2ZW50VmVudWUgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZEV2ZW50KSByZXR1cm47XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIC8vIENsb3NlIHRoZSBkaWFsb2dcbiAgICAgIHNldENvbmZpcm1EaWFsb2dPcGVuKGZhbHNlKTtcbiAgICAgIFxuICAgICAgLy8gTWFyayB0aGlzIGV2ZW50IGFzIGJlaW5nIGZpeGVkXG4gICAgICBzZXRGaXhpbmdFdmVudHMocHJldiA9PiAoeyAuLi5wcmV2LCBbc2VsZWN0ZWRFdmVudC5pZF06IHRydWUgfSkpO1xuICAgICAgXG4gICAgICBjb25zdCB0b2tlbiA9IGF3YWl0IGdldEFjY2Vzc1Rva2VuKCk7XG4gICAgICBjb25zdCBiYXNlVXJsID0gZ2V0QXBpVXJsKCk7XG4gICAgICBcbiAgICAgIC8vIENyZWF0ZSB0aGUgcmVxdWVzdCBib2R5IHdpdGggZXZlbnRJZCBhbmQgdmVudWVJZFxuICAgICAgY29uc3QgcmVxdWVzdEJvZHkgPSB7XG4gICAgICAgIGV2ZW50SWQ6IHNlbGVjdGVkRXZlbnQuaWQsXG4gICAgICAgIHZlbnVlSWQ6IHNlbGVjdGVkRXZlbnQuYXp1cmVfdmVudWVfaWQgLy8gVXNpbmcgdGhlIEF6dXJlIHZlbnVlIElEIGZyb20gdGhlIHNlbGVjdGVkIGV2ZW50XG4gICAgICB9O1xuICAgICAgXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2Jhc2VVcmx9L2FkbWluL2V2ZW50LXZlbnVlLXJlbGF0aW9ucy9maXhgLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHJlcXVlc3RCb2R5KVxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZml4IGV2ZW50LXZlbnVlIHJlbGF0aW9uc2hpcCBmb3IgJHtzZWxlY3RlZEV2ZW50Lm5hbWV9YCk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIFxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ1N1Y2Nlc3MnLFxuICAgICAgICBkZXNjcmlwdGlvbjogYEV2ZW50IFwiJHtzZWxlY3RlZEV2ZW50Py5uYW1lfVwiIHZlbnVlIHVwZGF0ZWQgdG8gXCIke3NlbGVjdGVkRXZlbnQ/LmF6dXJlX3ZlbnVlX25hbWV9XCJgLFxuICAgICAgICB2YXJpYW50OiAnZGVmYXVsdCdcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICAvLyBSZWZyZXNoIGRhdGEgYWZ0ZXIgc3VjY2Vzc2Z1bCBmaXhcbiAgICAgIGZldGNoRGF0YSgpO1xuICAgIH0gY2F0Y2ggKGVycjogdW5rbm93bikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZml4aW5nIGV2ZW50LXZlbnVlIHJlbGF0aW9uc2hpcCcsIGVycik7XG4gICAgICBcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6ICdFcnJvcicsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogYEZhaWxlZCB0byBmaXggZXZlbnQtdmVudWUgcmVsYXRpb25zaGlwIGZvciAke3NlbGVjdGVkRXZlbnQ/Lm5hbWV9YCxcbiAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJ1xuICAgICAgfSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIC8vIENsZWFyIHRoZSBmaXhpbmcgc3RhdGUgZm9yIHRoaXMgZXZlbnRcbiAgICAgIGlmIChzZWxlY3RlZEV2ZW50KSB7XG4gICAgICAgIHNldEZpeGluZ0V2ZW50cyhwcmV2ID0+ICh7IC4uLnByZXYsIFtzZWxlY3RlZEV2ZW50LmlkXTogZmFsc2UgfSkpO1xuICAgICAgfVxuICAgICAgLy8gQ2xlYXIgdGhlIHNlbGVjdGVkIGV2ZW50XG4gICAgICBzZXRTZWxlY3RlZEV2ZW50KG51bGwpO1xuICAgIH1cbiAgfTtcbiAgXG4gIC8vIE9yaWdpbmFsIGZ1bmN0aW9uIHRoYXQgd2FzIHVzZWQgd2l0aCB0aGUgRml4VmVudWVEaWFsb2cgY29tcG9uZW50XG4gIC8vIE5vdyBhZGFwdGVkIHRvIHdvcmsgd2l0aCBvdXIgY2VudHJhbGl6ZWQgY29uZmlybWF0aW9uIGRpYWxvZ1xuICBhc3luYyBmdW5jdGlvbiBoYW5kbGVGaXhFdmVudFZlbnVlX2xlZ2FjeShldmVudElkOiBzdHJpbmcsIGF6dXJlVmVudWVJZDogc3RyaW5nKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHRva2VuID0gYXdhaXQgZ2V0QWNjZXNzVG9rZW4oKTtcbiAgICAgIGNvbnN0IGJhc2VVcmwgPSBnZXRBcGlVcmwoKTtcbiAgICAgIFxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtiYXNlVXJsfS9hZG1pbi9ldmVudC12ZW51ZS1yZWxhdGlvbnMvZml4YCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAsXG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgZXZlbnRJZCxcbiAgICAgICAgICB2ZW51ZUlkOiBhenVyZVZlbnVlSWRcbiAgICAgICAgfSlcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZpeCBldmVudC12ZW51ZSByZWxhdGlvbnNoaXAnKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAnU3VjY2VzcycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnRXZlbnQgdmVudWUgcmVsYXRpb25zaGlwIGZpeGVkIHN1Y2Nlc3NmdWxseScsXG4gICAgICAgIHZhcmlhbnQ6ICdkZWZhdWx0J1xuICAgICAgfSk7XG4gICAgICBcbiAgICAgIC8vIFJlZnJlc2ggZGF0YVxuICAgICAgZmV0Y2hEYXRhKCk7XG4gICAgfSBjYXRjaCAoZXJyOiB1bmtub3duKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmaXhpbmcgZXZlbnQtdmVudWUgcmVsYXRpb25zaGlwJywgZXJyKTtcbiAgICAgIFxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ0Vycm9yJyxcbiAgICAgICAgZGVzY3JpcHRpb246IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGZpeCBldmVudC12ZW51ZSByZWxhdGlvbnNoaXAnLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICB9KTtcbiAgICB9XG4gIH1cblxuICAvLyBEZXRlcm1pbmUgc3RhdHVzIG1lc3NhZ2UgYW5kIGljb24gY29sb3JcbiAgY29uc3QgZ2V0U3RhdHVzRGV0YWlscyA9ICgpID0+IHtcbiAgICBpZiAoIWNvdW50cykgcmV0dXJuIHsgbWVzc2FnZTogJ0xvYWRpbmcuLi4nLCBzdGF0dXM6ICduZXV0cmFsJyB9O1xuICAgIFxuICAgIC8vIENoZWNrIGlmIHRoZXJlIGFyZSBhbnkgYWN0dWFsIHByb2JsZW1hdGljIHJlbGF0aW9uc2hpcHMgaW4gdGhlIGRyb3Bkb3duIGxpc3RzXG4gICAgY29uc3QgaGFzUHJvYmxlbWF0aWNSZWxhdGlvbnMgPSBtaXNzaW5nRXZlbnRzLmxlbmd0aCA+IDAgfHwgbWlzbWF0Y2hlZEV2ZW50cy5sZW5ndGggPiAwO1xuICAgIFxuICAgIC8vIElmIHRoZXJlIGFyZSBubyBwcm9ibGVtYXRpYyByZWxhdGlvbnMgdG8gdHJvdWJsZXNob290LCBzaG93IGdyZWVuXG4gICAgLy8gT25seSBzaG93IHllbGxvdyB3YXJuaW5nIHdoZW4gdGhlcmUncyBzb21ldGhpbmcgdGhhdCBuZWVkcyBhdHRlbnRpb25cbiAgICBpZiAoIWhhc1Byb2JsZW1hdGljUmVsYXRpb25zKSB7XG4gICAgICByZXR1cm4geyBcbiAgICAgICAgbWVzc2FnZTogY291bnRzLm1pc3NpbmdWZW51ZXMgPT09IDBcbiAgICAgICAgICA/ICdBbGwgZXZlbnRzIGhhdmUgdmVudWUgcmVsYXRpb25zaGlwcycgXG4gICAgICAgICAgOiBgJHtjb3VudHMubWlzc2luZ1ZlbnVlc30gZXZlbnRzIHdpdGggbm8gdmVudWUgcmVsYXRpb25zYCwgXG4gICAgICAgIHN0YXR1czogJ2dvb2QnIFxuICAgICAgfTtcbiAgICB9XG4gICAgXG4gICAgLy8gU2hvdyB5ZWxsb3cgd2FybmluZyB3aGVuIHRoZXJlIGFyZSBpdGVtcyBpbiB0aGUgZHJvcGRvd24gbGlzdHMgdGhhdCBuZWVkIGF0dGVudGlvblxuICAgIHJldHVybiB7XG4gICAgICBtZXNzYWdlOiBgJHtjb3VudHMubWlzc2luZ1ZlbnVlc30gZXZlbnRzIHdpdGggbm8gdmVudWUgcmVsYXRpb25zYCxcbiAgICAgIHN0YXR1czogJ3dhcm5pbmcnXG4gICAgfTtcbiAgfTtcbiAgXG4gIC8vIEdldCB0aGUgYXBwcm9wcmlhdGUgaWNvbiBjb2xvciBiYXNlZCBvbiBzdGF0dXNcbiAgY29uc3QgZ2V0SWNvbkNvbG9yQ2xhc3MgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnZ29vZCc6IHJldHVybiAndGV4dC1ncmVlbi01MDAnO1xuICAgICAgY2FzZSAnd2FybmluZyc6IHJldHVybiAndGV4dC15ZWxsb3ctNTAwJztcbiAgICAgIGNhc2UgJ2JhZCc6IHJldHVybiAndGV4dC1yZWQtNTAwJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAndGV4dC1tdXRlZC1mb3JlZ3JvdW5kJztcbiAgICB9XG4gIH07XG4gIFxuICBjb25zdCBzdGF0dXNEZXRhaWxzID0gZ2V0U3RhdHVzRGV0YWlscygpO1xuICBcbiAgLy8gRm9ybWF0IGRhdGUgZm9yIGRpc3BsYXlcbiAgY29uc3QgZm9ybWF0RGF0ZSA9IChkYXRlU3RyaW5nOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWRhdGVTdHJpbmcpIHJldHVybiAnTm8gZGF0ZSc7XG4gICAgXG4gICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVTdHJpbmcpO1xuICAgIHJldHVybiBkYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tVVMnLCB7XG4gICAgICB5ZWFyOiAnbnVtZXJpYycsXG4gICAgICBtb250aDogJ3Nob3J0JyxcbiAgICAgIGRheTogJ251bWVyaWMnXG4gICAgfSk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8Q2FyZD5cbiAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgaXRlbXMtY2VudGVyIHNwYWNlLXktMCBwYi0yXCI+XG4gICAgICAgIDxHaXRNZXJnZSBjbGFzc05hbWU9e2BoLTUgdy01IG1yLTIgJHtnZXRJY29uQ29sb3JDbGFzcyhzdGF0dXNEZXRhaWxzLnN0YXR1cyl9YH0gLz5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQteGxcIj5FdmVudHMgLSBWZW51ZXM8L0NhcmRUaXRsZT5cbiAgICAgICAgICB7LyogPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5NYW5hZ2UgcmVsYXRpb25zaGlwcyBiZXR3ZWVuIGV2ZW50cyBhbmQgdmVudWVzPC9wPiAqL31cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICBcbiAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAge2lzTG9hZGluZyA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtOCB3LTggYW5pbWF0ZS1zcGluIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+TG9hZGluZyByZWxhdGlvbnNoaXAgZGF0YS4uLjwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IGVycm9yID8gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHktOCBweC00XCI+XG4gICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXJlZC01MDAgbXItMlwiIC8+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTUwMFwiPntlcnJvcn08L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgey8qIFN0YXR1cyBpbmRpY2F0b3IgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e3N0YXR1c0RldGFpbHMubWVzc2FnZX08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgey8qIEFjdGlvbiBidXR0b25zICovfVxuICAgICAgICAgICAgICB7LyogQ29tbWVudGVkIG91dCBhcyByZXF1ZXN0ZWQgLSBnbG9iYWwgZml4ZXMgd291bGQgdGFrZSB0b28gbG9uZyBmb3IgVUkgb3BlcmF0aW9uc1xuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUZpeEFsbH0gXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNCdWxrRml4aW5nIHx8IGNvdW50cz8ubWlzc2luZ1ZlbnVlcyA9PT0gMH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7aXNCdWxrRml4aW5nID8gKFxuICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNCBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIEZpeGluZy4uLlxuICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIEZpeCBBbGwgUmVsYXRpb25zaGlwc1xuICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCIgXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtmZXRjaERhdGF9IFxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZyB8fCBpc0J1bGtGaXhpbmd9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgUmVmcmVzaFxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKi99XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7LyogQmF0Y2ggRml4LCBDU1YgRml4LCBhbmQgUmVmcmVzaCBidXR0b25zICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgey8qIEJhdGNoIEZpeCBidXR0b24gLSBmaXhlcyB1cCB0byAxMCBldmVudHMgYXQgYSB0aW1lICovfVxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUJhdGNoRml4fVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2JhdGNoRml4aW5nIHx8IGNzdkZpeGluZyB8fCBtaXNzaW5nRXZlbnRzLmxlbmd0aCA9PT0gMH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCJcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7YmF0Y2hGaXhpbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00IGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgRml4aW5nIHtiYXRjaEZpeFByb2dyZXNzLmN1cnJlbnR9L3tiYXRjaEZpeFByb2dyZXNzLnRvdGFsfS4uLlxuICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgPFdyZW5jaCBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIEJhdGNoIEZpeCAoMTAgRXZlbnRzKVxuICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgICAgICB7LyogQ1NWIEZpeCBidXR0b24gLSB2YWxpZGF0ZXMgYWxsIHJlbGF0aW9uc2hpcHMgZnJvbSBDU1YgKi99XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ3N2Rml4fVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2JhdGNoRml4aW5nIHx8IGNzdkZpeGluZ31cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCJcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7Y3N2Rml4aW5nID8gKFxuICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNCBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIFZhbGlkYXRpbmcgQ1NWLi4uXG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2sgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBWYWxpZGF0ZSBmcm9tIENTVlxuICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgICAgICB7LyogUmVmcmVzaCBidXR0b24gKi99XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cImljb25cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17ZmV0Y2hEYXRhfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZyB8fCBiYXRjaEZpeGluZyB8fCBjc3ZGaXhpbmd9XG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIlJlZnJlc2ggZGF0YVwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7LyogQmF0Y2ggZml4IHByb2dyZXNzIGluZGljYXRvciAqL31cbiAgICAgICAgICAgICAge2JhdGNoRml4aW5nICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgcC0yIGJnLW11dGVkIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBtYi0xIGZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPkJhdGNoIEZpeCBQcm9ncmVzczwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+e2JhdGNoRml4UHJvZ3Jlc3MuY3VycmVudH0ve2JhdGNoRml4UHJvZ3Jlc3MudG90YWx9IGV2ZW50czwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctc2Vjb25kYXJ5IGgtMiByb3VuZGVkLWZ1bGwgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcHJpbWFyeSBoLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0XCIgXG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAkeyhiYXRjaEZpeFByb2dyZXNzLmN1cnJlbnQgLyBNYXRoLm1heChiYXRjaEZpeFByb2dyZXNzLnRvdGFsLCAxKSkgKiAxMDB9JWAgfX1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXhzIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi01MDBcIj57YmF0Y2hGaXhQcm9ncmVzcy5zdWNjZXNzfSBzdWNjZXNzZnVsPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDBcIj57YmF0Y2hGaXhQcm9ncmVzcy5mYWlsZWR9IGZhaWxlZDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgey8qIEV2ZW50IHNlY3Rpb25zICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIHsvKiBNaXNzaW5nIFZlbnVlcyBzZWN0aW9uICovfVxuICAgICAgICAgICAgICAgIDxDb2xsYXBzaWJsZSBcbiAgICAgICAgICAgICAgICAgIG9wZW49e2V4cGFuZGVkLm1pc3Npbmd9IFxuICAgICAgICAgICAgICAgICAgb25PcGVuQ2hhbmdlPXsoKSA9PiB0b2dnbGVFeHBhbmRlZCgnbWlzc2luZycpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIHJvdW5kZWQtbWRcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxDb2xsYXBzaWJsZVRyaWdnZXIgY2xhc3NOYW1lPVwiZmxleCB3LWZ1bGwgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTQgdGV4dC1sZWZ0IGhvdmVyOmJnLW11dGVkLzUwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2FsZW5kYXIgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5FdmVudHMgTWlzc2luZyBWZW51ZSBSZWxhdGlvbnNoaXBzICh7bWlzc2luZ0V2ZW50cy5sZW5ndGh9KTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIHtleHBhbmRlZC5taXNzaW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uRG93biBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9Db2xsYXBzaWJsZVRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxDb2xsYXBzaWJsZUNvbnRlbnQgY2xhc3NOYW1lPVwicHgtNCBwYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIHttaXNzaW5nRXZlbnRzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBweS0yXCI+Tm8gZXZlbnRzIG1pc3NpbmcgdmVudWVzPC9wPlxuICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlUm93PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+RXZlbnQgTmFtZTwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+RGF0ZTwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+QXp1cmUgVmVudWU8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPlBvc3RncmVTUUwgVmVudWU8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPkFjdGlvbnM8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZVJvdz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVCb2R5PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bWlzc2luZ0V2ZW50cy5tYXAoZXZlbnQgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZVJvdyBrZXk9e2V2ZW50LmlkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57ZXZlbnQubmFtZX08L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+e2Zvcm1hdERhdGUoZXZlbnQuZGF0ZSl9PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPntldmVudC5henVyZV92ZW51ZV9uYW1lfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDBcIj57ZXZlbnQucG9zdGdyZXNfdmVudWVfbmFtZSB8fCAnTWlzc2luZyd9PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIiBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9wZW5GaXhDb25maXJtRGlhbG9nKGV2ZW50KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17Zml4aW5nRXZlbnRzW2V2ZW50LmlkXX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmaXhpbmdFdmVudHNbZXZlbnQuaWRdID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwibXItMSBoLTMgdy0zIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEZpeGluZy4uLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiRml4XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUJvZHk+XG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZT5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvQ29sbGFwc2libGVDb250ZW50PlxuICAgICAgICAgICAgICAgIDwvQ29sbGFwc2libGU+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgey8qIE1pc21hdGNoZWQgVmVudWVzIHNlY3Rpb24gKi99XG4gICAgICAgICAgICAgICAgPENvbGxhcHNpYmxlIFxuICAgICAgICAgICAgICAgICAgb3Blbj17ZXhwYW5kZWQubWlzbWF0Y2hlZH0gXG4gICAgICAgICAgICAgICAgICBvbk9wZW5DaGFuZ2U9eygpID0+IHRvZ2dsZUV4cGFuZGVkKCdtaXNtYXRjaGVkJyl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXIgcm91bmRlZC1tZFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPENvbGxhcHNpYmxlVHJpZ2dlciBjbGFzc05hbWU9XCJmbGV4IHctZnVsbCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCB0ZXh0LWxlZnQgaG92ZXI6YmctbXV0ZWQvNTAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxDYWxlbmRhciBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkV2ZW50cyB3aXRoIE1pc21hdGNoZWQgVmVudWUgKHttaXNtYXRjaGVkRXZlbnRzLmxlbmd0aH0pPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAge2V4cGFuZGVkLm1pc21hdGNoZWQgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25Eb3duIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L0NvbGxhcHNpYmxlVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPENvbGxhcHNpYmxlQ29udGVudCBjbGFzc05hbWU9XCJweC00IHBiLTRcIj5cbiAgICAgICAgICAgICAgICAgICAge21pc21hdGNoZWRFdmVudHMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIHB5LTJcIj5ObyBldmVudHMgd2l0aCBtaXNtYXRjaGVkIHZlbnVlczwvcD5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZVJvdz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPkV2ZW50IE5hbWU8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPkRhdGU8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPkF6dXJlIFZlbnVlPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlSGVhZD5Qb3N0Z3JlU1FMIFZlbnVlPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlSGVhZD5BY3Rpb25zPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQm9keT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge21pc21hdGNoZWRFdmVudHMubWFwKGV2ZW50ID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVSb3cga2V5PXtldmVudC5pZH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2V2ZW50Lm5hbWV9PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPntmb3JtYXREYXRlKGV2ZW50LmRhdGUpfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD57ZXZlbnQuYXp1cmVfdmVudWVfbmFtZX08L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNTAwXCI+e2V2ZW50LnBvc3RncmVzX3ZlbnVlX25hbWV9PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIiBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9wZW5GaXhDb25maXJtRGlhbG9nKGV2ZW50KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17Zml4aW5nRXZlbnRzW2V2ZW50LmlkXX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmaXhpbmdFdmVudHNbZXZlbnQuaWRdID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwibXItMSBoLTMgdy0zIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEZpeGluZy4uLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiRml4XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUJvZHk+XG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZT5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvQ29sbGFwc2libGVDb250ZW50PlxuICAgICAgICAgICAgICAgIDwvQ29sbGFwc2libGU+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC8+XG4gICAgICAgICl9XG5cbiAgICAgIHsvKiBDb25maXJtYXRpb24gRGlhbG9nICovfVxuICAgICAgPERpYWxvZyBvcGVuPXtjb25maXJtRGlhbG9nT3Blbn0gb25PcGVuQ2hhbmdlPXtzZXRDb25maXJtRGlhbG9nT3Blbn0+XG4gICAgICAgIDxEaWFsb2dDb250ZW50PlxuICAgICAgICAgIDxEaWFsb2dIZWFkZXI+XG4gICAgICAgICAgICA8RGlhbG9nVGl0bGU+Rml4IEV2ZW50LVZlbnVlIFJlbGF0aW9uc2hpcDwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgICA8RGlhbG9nRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgIHtzZWxlY3RlZEV2ZW50ICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNCBweS0yXCI+XG4gICAgICAgICAgICAgICAgICA8cD5BcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZml4IHRoZSB2ZW51ZSByZWxhdGlvbnNoaXAgZm9yIHRoaXMgZXZlbnQ/PC9wPlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLW11dGVkIHAtMyByb3VuZGVkLW1kIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgPHA+PHN0cm9uZz5FdmVudDo8L3N0cm9uZz4ge3NlbGVjdGVkRXZlbnQ/Lm5hbWV9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8cD48c3Ryb25nPkRhdGU6PC9zdHJvbmc+IHtmb3JtYXREYXRlKHNlbGVjdGVkRXZlbnQ/LmRhdGUgfHwgJycpfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHA+PHN0cm9uZz5DdXJyZW50IFZlbnVlOjwvc3Ryb25nPiB7c2VsZWN0ZWRFdmVudD8ucG9zdGdyZXNfdmVudWVfbmFtZSB8fCAnTm9uZSd9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8cD48c3Ryb25nPlRhcmdldCBWZW51ZTo8L3N0cm9uZz4ge3NlbGVjdGVkRXZlbnQ/LmF6dXJlX3ZlbnVlX25hbWV9PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxwPlRoaXMgd2lsbDo8L3A+XG4gICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibGlzdC1kaXNjIHBsLTUgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxsaT5VcGRhdGUgdGhlIGV2ZW50IHRvIHJlZmVyZW5jZSB0aGUgY29ycmVjdCB2ZW51ZSBmcm9tIEF6dXJlPC9saT5cbiAgICAgICAgICAgICAgICAgICAgPGxpPkNyZWF0ZSB0aGUgdmVudWUgaW4gUG9zdGdyZVNRTCBpZiBpdCBkb2Vzbid0IGV4aXN0PC9saT5cbiAgICAgICAgICAgICAgICAgICAgPGxpPlVwZGF0ZSB0aGUgZXZlbnQncyB2ZW51ZV9pZCB0byBtYWludGFpbiBkYXRhIGNvbnNpc3RlbmN5PC9saT5cbiAgICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L0RpYWxvZ0Rlc2NyaXB0aW9uPlxuICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxuICAgICAgICAgIDxEaWFsb2dGb290ZXI+XG4gICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17KCkgPT4gc2V0Q29uZmlybURpYWxvZ09wZW4oZmFsc2UpfT5DYW5jZWw8L0J1dHRvbj5cbiAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17aGFuZGxlRml4RXZlbnRWZW51ZX0+Rml4PC9CdXR0b24+XG4gICAgICAgICAgPC9EaWFsb2dGb290ZXI+XG4gICAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICAgIDwvRGlhbG9nPlxuICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgPC9DYXJkPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkdpdE1lcmdlIiwiTG9hZGVyMiIsIkFsZXJ0Q2lyY2xlIiwiQ2FsZW5kYXIiLCJDaGV2cm9uRG93biIsIkNoZXZyb25SaWdodCIsIkNoZWNrIiwiV3JlbmNoIiwiUmVmcmVzaEN3IiwidXNlQXV0aCIsIkNvbGxhcHNpYmxlIiwiQ29sbGFwc2libGVDb250ZW50IiwiQ29sbGFwc2libGVUcmlnZ2VyIiwiQnV0dG9uIiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0Rlc2NyaXB0aW9uIiwiRGlhbG9nRm9vdGVyIiwiRGlhbG9nSGVhZGVyIiwiRGlhbG9nVGl0bGUiLCJUYWJsZSIsIlRhYmxlQm9keSIsIlRhYmxlQ2VsbCIsIlRhYmxlSGVhZCIsIlRhYmxlSGVhZGVyIiwiVGFibGVSb3ciLCJ1c2VUb2FzdCIsImdldEFwaVVybCIsIkV2ZW50VmVudWVSZWxhdGlvbnMiLCJjb3VudHMiLCJzZXRDb3VudHMiLCJtaXNzaW5nRXZlbnRzIiwic2V0TWlzc2luZ0V2ZW50cyIsIm1pc21hdGNoZWRFdmVudHMiLCJzZXRNaXNtYXRjaGVkRXZlbnRzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiaXNCdWxrRml4aW5nIiwic2V0SXNCdWxrRml4aW5nIiwiYmF0Y2hGaXhpbmciLCJzZXRCYXRjaEZpeGluZyIsImNzdkZpeGluZyIsInNldENzdkZpeGluZyIsImJhdGNoRml4UHJvZ3Jlc3MiLCJzZXRCYXRjaEZpeFByb2dyZXNzIiwiY3VycmVudCIsInRvdGFsIiwic3VjY2VzcyIsImZhaWxlZCIsImZpeGluZ0V2ZW50cyIsInNldEZpeGluZ0V2ZW50cyIsImVycm9yIiwic2V0RXJyb3IiLCJleHBhbmRlZCIsInNldEV4cGFuZGVkIiwibWlzc2luZyIsIm1pc21hdGNoZWQiLCJjb25maXJtRGlhbG9nT3BlbiIsInNldENvbmZpcm1EaWFsb2dPcGVuIiwic2VsZWN0ZWRFdmVudCIsInNldFNlbGVjdGVkRXZlbnQiLCJnZXRBY2Nlc3NUb2tlbiIsInRvYXN0IiwiZmV0Y2hEYXRhIiwidG9rZW4iLCJiYXNlVXJsIiwiY291bnRzUmVzcG9uc2UiLCJmZXRjaCIsImhlYWRlcnMiLCJvayIsIkVycm9yIiwiY291bnRzRGF0YSIsImpzb24iLCJtaXNzaW5nUmVzcG9uc2UiLCJtaXNzaW5nRGF0YSIsIm1pc21hdGNoZWRSZXNwb25zZSIsIm1pc21hdGNoZWREYXRhIiwiZXJyIiwiY29uc29sZSIsIm1lc3NhZ2UiLCJ0b2dnbGVFeHBhbmRlZCIsInNlY3Rpb24iLCJwcmV2IiwiaGFuZGxlRml4QWxsIiwicmVzcG9uc2UiLCJtZXRob2QiLCJyZXN1bHQiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwidmFyaWFudCIsImhhbmRsZUJhdGNoRml4IiwiZXZlbnRzVG9GaXgiLCJzbGljZSIsImxlbmd0aCIsIm5ld0ZpeGluZ1N0YXRlIiwiZm9yRWFjaCIsImV2ZW50IiwiaWQiLCJsb2ciLCJpIiwibmFtZSIsInJlcXVlc3RCb2R5IiwiZXZlbnRJZCIsInZlbnVlSWQiLCJhenVyZV92ZW51ZV9pZCIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5Iiwic3RhdHVzIiwiZXZlbnRFcnJvciIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImhhbmRsZUNzdkZpeCIsImVycm9yVGV4dCIsInRleHQiLCJzdGF0dXNUZXh0Iiwic3RhdHMiLCJ2YWxpZGF0ZWRSZWxhdGlvbnNoaXBzIiwib3BlbkZpeENvbmZpcm1EaWFsb2ciLCJoYW5kbGVGaXhFdmVudFZlbnVlIiwiYXp1cmVfdmVudWVfbmFtZSIsImhhbmRsZUZpeEV2ZW50VmVudWVfbGVnYWN5IiwiYXp1cmVWZW51ZUlkIiwiZ2V0U3RhdHVzRGV0YWlscyIsImhhc1Byb2JsZW1hdGljUmVsYXRpb25zIiwibWlzc2luZ1ZlbnVlcyIsImdldEljb25Db2xvckNsYXNzIiwic3RhdHVzRGV0YWlscyIsImZvcm1hdERhdGUiLCJkYXRlU3RyaW5nIiwiZGF0ZSIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJ5ZWFyIiwibW9udGgiLCJkYXkiLCJjbGFzc05hbWUiLCJkaXYiLCJwIiwib25DbGljayIsImRpc2FibGVkIiwic2l6ZSIsInNwYW4iLCJzdHlsZSIsIndpZHRoIiwiTWF0aCIsIm1heCIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJtYXAiLCJwb3N0Z3Jlc192ZW51ZV9uYW1lIiwic3Ryb25nIiwidWwiLCJsaSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/event-relations/venue.tsx\n"));

/***/ })

});