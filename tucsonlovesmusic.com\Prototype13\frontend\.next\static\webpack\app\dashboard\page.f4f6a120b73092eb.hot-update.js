"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/admin/id-validation/index.tsx":
/*!**************************************************!*\
  !*** ./components/admin/id-validation/index.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IdValidation: () => (/* binding */ IdValidation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-auth */ \"(app-pages-browser)/./hooks/use-auth.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./lib/config.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _merge_dialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./merge-dialog */ \"(app-pages-browser)/./components/admin/id-validation/merge-dialog.tsx\");\n/* harmony import */ var _fix_dialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./fix-dialog */ \"(app-pages-browser)/./components/admin/id-validation/fix-dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ IdValidation auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction IdValidation() {\n    var _issuesByType_events, _selectedIssue_postgresRecord, _selectedIssue_azureRecord, _selectedIssue_postgresRecord1, _selectedIssue_azureRecord1, _selectedIssue_postgresRecord2, _selectedIssue_azureRecord2;\n    _s();\n    // State for validation data\n    const [validationCounts, setValidationCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [issuesByType, setIssuesByType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loadingCounts, setLoadingCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingIssues, setLoadingIssues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeEntityType, setActiveEntityType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Dialogs state\n    const [fixDialogOpen, setFixDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mergeDialogOpen, setMergeDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIssue, setSelectedIssue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isMerging, setIsMerging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMatchId, setSelectedMatchId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Expanded state for collapsible sections\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        venues: false,\n        musicians: false,\n        events: false\n    });\n    const [syncResults, setSyncResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageFixResults, setImageFixResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSyncing, setIsSyncing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFixingImages, setIsFixingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchFixing, setIsBatchFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [batchFixResults, setBatchFixResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { getAccessToken } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    // Toggle expanded state for a specific section\n    const toggleExpanded = (section)=>{\n        setExpanded((prev)=>({\n                ...prev,\n                [section]: !prev[section]\n            }));\n        // If we're expanding a section and don't have the data yet, fetch it\n        if (!expanded[section] && section === 'venues' && (!issuesByType.venues || issuesByType.venues.length === 0)) {\n            fetchIssues('venues');\n        } else if (!expanded[section] && section === 'musicians' && (!issuesByType.musicians || issuesByType.musicians.length === 0)) {\n            fetchIssues('musicians');\n        } else if (!expanded[section] && section === 'events' && (!issuesByType.events || issuesByType.events.length === 0)) {\n            fetchIssues('events');\n        }\n    };\n    // Fetch validation counts on load\n    // Complete refresh function - exactly matching duplicate detection approach\n    const refreshData = async ()=>{\n        try {\n            setLoadingCounts(true);\n            setError(null);\n            const apiUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const token = await getAccessToken();\n            // Fetch fresh validation counts\n            const countsResponse = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/counts\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (!countsResponse.ok) {\n                throw new Error(\"Failed to fetch validation counts\");\n            }\n            const data = await countsResponse.json();\n            console.log('Fetched validation counts:', data);\n            // Update counts\n            setValidationCounts(data);\n            // CRITICAL: Complete reset of all issues data\n            // This is the key difference from our previous approach\n            setIssuesByType({});\n            // Fetch venue issues if count > 0 and section is expanded\n            if (data.venueCount > 0 && (expanded.venues || activeEntityType === 'venues')) {\n                console.log('Fetching venue validation issues');\n                const timestamp = new Date().getTime(); // Add cache busting\n                const venueResponse = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/issues/venues?t=\").concat(timestamp), {\n                    method: \"GET\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(token),\n                        \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                        \"Pragma\": \"no-cache\"\n                    }\n                });\n                if (venueResponse.ok) {\n                    const venueData = await venueResponse.json();\n                    console.log(\"Received \".concat(venueData.length, \" venue issues\"));\n                    setIssuesByType((prev)=>({\n                            ...prev,\n                            venues: venueData\n                        }));\n                }\n            }\n            // Fetch musician issues if count > 0 and section is expanded\n            if (data.musicianCount > 0 && (expanded.musicians || activeEntityType === 'musicians')) {\n                console.log('Fetching musician validation issues');\n                const timestamp = new Date().getTime(); // Add cache busting\n                const musicianResponse = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/issues/musicians?t=\").concat(timestamp), {\n                    method: \"GET\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(token),\n                        \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                        \"Pragma\": \"no-cache\"\n                    }\n                });\n                if (musicianResponse.ok) {\n                    const musicianData = await musicianResponse.json();\n                    console.log(\"Received \".concat(musicianData.length, \" musician issues\"));\n                    setIssuesByType((prev)=>({\n                            ...prev,\n                            musicians: musicianData\n                        }));\n                }\n            }\n            // Fetch event issues if count > 0 and section is expanded\n            if (data.eventCount > 0 && (expanded.events || activeEntityType === 'events')) {\n                console.log('Fetching event validation issues');\n                const timestamp = new Date().getTime(); // Add cache busting\n                const eventResponse = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/issues/events?t=\").concat(timestamp), {\n                    method: \"GET\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(token),\n                        \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                        \"Pragma\": \"no-cache\"\n                    }\n                });\n                if (eventResponse.ok) {\n                    const eventData = await eventResponse.json();\n                    console.log(\"Received \".concat(eventData.length, \" event issues\"));\n                    setIssuesByType((prev)=>({\n                            ...prev,\n                            events: eventData\n                        }));\n                }\n            }\n            return data;\n        } catch (err) {\n            console.error(\"Error refreshing data:\", err);\n            setError(\"Failed to refresh validation data. Please try again.\");\n            toast({\n                title: \"Error\",\n                description: \"Failed to refresh validation data. Please try again.\",\n                variant: \"destructive\"\n            });\n            return null;\n        } finally{\n            setLoadingCounts(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IdValidation.useEffect\": ()=>{\n            refreshData();\n        }\n    }[\"IdValidation.useEffect\"], []);\n    // Fetch issues when an entity type is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IdValidation.useEffect\": ()=>{\n            if (activeEntityType) {\n                fetchIssues(activeEntityType);\n            }\n        }\n    }[\"IdValidation.useEffect\"], [\n        activeEntityType\n    ]);\n    // Fetch validation counts\n    const fetchValidationCounts = async ()=>{\n        try {\n            setLoadingCounts(true);\n            setError(null);\n            const apiUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const token = await getAccessToken();\n            const response = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/counts\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch validation counts\");\n            }\n            const data = await response.json();\n            setValidationCounts(data);\n            // Return the data for direct use\n            return data;\n        } catch (err) {\n            console.error(\"Error fetching validation counts:\", err);\n            setError(\"Failed to fetch validation counts. Please try again.\");\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch validation counts. Please try again.\",\n                variant: \"destructive\"\n            });\n            // Return null in case of error\n            return null;\n        } finally{\n            setLoadingCounts(false);\n        }\n    };\n    // Fetch issues for a specific entity type\n    const fetchIssues = async (entityType)=>{\n        try {\n            setLoadingIssues(true);\n            setError(null);\n            const apiUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const token = await getAccessToken();\n            console.log(\"Fetching \".concat(entityType, \" issues\"));\n            const response = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/issues/\").concat(entityType), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch \".concat(entityType, \" issues\"));\n            }\n            const data = await response.json();\n            console.log(\"Received \".concat(data.length, \" \").concat(entityType, \" issues\"));\n            // Debug: Log the breakdown of issue types\n            const missingAzure = data.filter((issue)=>issue.issueType === 'missing_azure').length;\n            const missingPostgres = data.filter((issue)=>issue.issueType === 'missing_postgres').length;\n            console.log(\"Issue breakdown - missing_azure: \".concat(missingAzure, \", missing_postgres: \").concat(missingPostgres));\n            // Debug: Log a sample of each issue type if available\n            if (missingAzure > 0) {\n                console.log('Sample missing_azure issue:', data.find((issue)=>issue.issueType === 'missing_azure'));\n            }\n            if (missingPostgres > 0) {\n                console.log('Sample missing_postgres issue:', data.find((issue)=>issue.issueType === 'missing_postgres'));\n            }\n            // Apply a complete replacement of data rather than merging\n            setIssuesByType((prev)=>({\n                    ...prev,\n                    [entityType]: data\n                }));\n            return data;\n        } catch (err) {\n            console.error(\"Error fetching \".concat(entityType, \" issues:\"), err);\n            setError(\"Failed to fetch \".concat(entityType, \" issues. Please try again.\"));\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch \".concat(entityType, \" issues. Please try again.\"),\n                variant: \"destructive\"\n            });\n            return [];\n        } finally{\n            setLoadingIssues(false);\n        }\n    };\n    // Handle opening fix dialog for an issue\n    const handleFixIssue = (issue)=>{\n        setSelectedIssue(issue);\n        setFixDialogOpen(true);\n    };\n    // Handle opening merge dialog for an issue\n    const handleMergeIssue = (issue)=>{\n        setSelectedIssue(issue);\n        setMergeDialogOpen(true);\n    };\n    // Handle resolving an issue\n    const handleResolveIssue = async (issueId, resolution, options)=>{\n        try {\n            setIsMerging(true);\n            const apiUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const token = await getAccessToken();\n            const payload = {\n                resolution\n            };\n            // Handle different types of the options parameter\n            if (typeof options === 'string') {\n                // It's a regular matchId\n                payload.matchId = options;\n            } else if (options && typeof options === 'object') {\n                // It's an options object with possible azureId and mergeFields\n                if ('azureId' in options) {\n                    payload.azureId = options.azureId;\n                }\n                if ('mergeFields' in options) {\n                    payload.mergeFields = options.mergeFields;\n                }\n            }\n            // Close any open dialogs right away for better UX\n            setFixDialogOpen(false);\n            setMergeDialogOpen(false);\n            // Now send the API request\n            const response = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/resolve/\").concat(issueId), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify(payload)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to resolve issue\");\n            }\n            toast({\n                title: \"Success\",\n                description: \"Issue resolved successfully\"\n            });\n            // Simple, direct approach - exactly like duplicate detection component\n            const latestCounts = await refreshData();\n            // Auto-collapse sections when they reach zero count\n            if (latestCounts) {\n                if (activeEntityType === 'venues' && latestCounts.venueCount === 0) {\n                    setExpanded((prev)=>({\n                            ...prev,\n                            venues: false\n                        }));\n                } else if (activeEntityType === 'musicians' && latestCounts.musicianCount === 0) {\n                    setExpanded((prev)=>({\n                            ...prev,\n                            musicians: false\n                        }));\n                } else if (activeEntityType === 'events' && latestCounts.eventCount === 0) {\n                    setExpanded((prev)=>({\n                            ...prev,\n                            events: false\n                        }));\n                }\n            }\n        } catch (err) {\n            console.error(\"Error resolving issue:\", err);\n            toast({\n                title: \"Error\",\n                description: \"Failed to resolve issue. Please try again.\",\n                variant: \"destructive\"\n            });\n            // If there was an error, do a complete refresh to ensure accurate state\n            await refreshData();\n        } finally{\n            setIsMerging(false);\n        }\n    };\n    // Handle merge for duplicate records\n    const handleMerge = async (primaryId, duplicateIds)=>{\n        try {\n            setIsMerging(true);\n            const apiUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const token = await getAccessToken();\n            const response = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/merge\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    primaryId,\n                    duplicateIds\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to merge records\");\n            }\n            toast({\n                title: \"Success\",\n                description: \"Records merged successfully\"\n            });\n            // Refresh the issues and counts\n            if (activeEntityType) {\n                fetchIssues(activeEntityType);\n            }\n            fetchValidationCounts();\n            // Close any open dialogs\n            setMergeDialogOpen(false);\n            return true;\n        } catch (error) {\n            console.error(\"Error merging records:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to merge records. Please try again.\",\n                variant: \"destructive\"\n            });\n            return false;\n        } finally{\n            setIsMerging(false);\n        }\n    };\n    // Open merge dialog when duplicate group is selected\n    const showMergeDialog = (ids, metadata)=>{\n        // Only open the merge dialog if there's a selected issue\n        if (selectedIssue) {\n            setMergeDialogOpen(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-5 w-5 \".concat(!validationCounts || parseInt(validationCounts.venueCount) === 0 && parseInt(validationCounts.musicianCount) === 0 && parseInt(validationCounts.eventCount) === 0 ? 'text-green-500' : 'text-yellow-500')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, this),\n                        \"ID Validation\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                    lineNumber: 497,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-destructive/10 border border-destructive rounded-md p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-5 w-5 text-destructive\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-medium text-destructive\",\n                                    children: [\n                                        error,\n                                        \". Please try again.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 11\n                    }, this),\n                    loadingCounts && !validationCounts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-8 w-8 animate-spin mx-auto mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading ID validation data...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.Collapsible, {\n                                            open: expanded.venues,\n                                            onOpenChange: ()=>toggleExpanded('venues'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Venue ID Validation Issues (\",\n                                                                        (validationCounts === null || validationCounts === void 0 ? void 0 : validationCounts.venueCount) || 0,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        expanded.venues ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: loadingIssues ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 animate-spin mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Loading venue issues...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 25\n                                                    }, this) : issuesByType.venues && issuesByType.venues.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 654,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"PostgreSQL ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 655,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Azure ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 656,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Issue Type\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 657,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 658,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                                                                children: issuesByType.venues.map((issue)=>{\n                                                                    var _issue_postgresRecord, _issue_azureRecord, _issue_postgresRecord1, _issue_azureRecord1;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: ((_issue_postgresRecord = issue.postgresRecord) === null || _issue_postgresRecord === void 0 ? void 0 : _issue_postgresRecord.name) || ((_issue_azureRecord = issue.azureRecord) === null || _issue_azureRecord === void 0 ? void 0 : _issue_azureRecord.name) || 'Unknown'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 664,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: ((_issue_postgresRecord1 = issue.postgresRecord) === null || _issue_postgresRecord1 === void 0 ? void 0 : _issue_postgresRecord1.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-mono text-xs\",\n                                                                                    children: [\n                                                                                        issue.postgresRecord.id.substring(0, 8),\n                                                                                        \"...\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 669,\n                                                                                    columnNumber: 37\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-muted-foreground text-xs italic\",\n                                                                                    children: \"Missing\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 671,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 667,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: ((_issue_azureRecord1 = issue.azureRecord) === null || _issue_azureRecord1 === void 0 ? void 0 : _issue_azureRecord1.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-mono text-xs\",\n                                                                                    children: [\n                                                                                        issue.azureRecord.id.substring(0, 8),\n                                                                                        \"...\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 676,\n                                                                                    columnNumber: 37\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-muted-foreground text-xs italic\",\n                                                                                    children: \"Missing\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 678,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 674,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                                    children: issue.issueType === 'missing_azure' ? 'missing azure' : issue.issueType.replace('_', ' ')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 682,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 681,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex space-x-2\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleFixIssue(issue),\n                                                                                        children: \"Fix\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 688,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 687,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 686,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, issue.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                        lineNumber: 663,\n                                                                        columnNumber: 31\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 661,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No venue ID validation issues found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                        lineNumber: 702,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.Collapsible, {\n                                            open: expanded.musicians,\n                                            onOpenChange: ()=>toggleExpanded('musicians'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 715,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Musician ID Validation Issues (\",\n                                                                        (validationCounts === null || validationCounts === void 0 ? void 0 : validationCounts.musicianCount) || 0,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 716,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        expanded.musicians ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: loadingIssues ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 animate-spin mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 728,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Loading musician issues...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 25\n                                                    }, this) : issuesByType.musicians && issuesByType.musicians.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 736,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"PostgreSQL ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 737,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Issue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 738,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Created\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 739,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 740,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                                                                children: issuesByType.musicians.map((issue)=>{\n                                                                    var _issue_postgresRecord, _issue_postgresRecord1;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                className: \"font-medium\",\n                                                                                children: ((_issue_postgresRecord = issue.postgresRecord) === null || _issue_postgresRecord === void 0 ? void 0 : _issue_postgresRecord.name) || 'Unknown'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 746,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                        className: \"bg-secondary/50 px-1 py-0.5 rounded text-xs\",\n                                                                                        children: issue.id\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 748,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"icon\",\n                                                                                        className: \"h-6 w-6 ml-1\",\n                                                                                        onClick: ()=>{\n                                                                                            navigator.clipboard.writeText(issue.id);\n                                                                                            toast({\n                                                                                                title: \"Copied to clipboard\",\n                                                                                                description: \"The musician ID has been copied to your clipboard.\"\n                                                                                            });\n                                                                                        },\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"h-3 w-3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 763,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 751,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 747,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: issue.issueType\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 766,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: ((_issue_postgresRecord1 = issue.postgresRecord) === null || _issue_postgresRecord1 === void 0 ? void 0 : _issue_postgresRecord1.createdAt) ? new Date(issue.postgresRecord.createdAt).toLocaleDateString() : 'Unknown'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 767,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex space-x-2\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleFixIssue(issue),\n                                                                                        children: \"Fix\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 774,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 773,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 772,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, issue.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No musician ID validation issues found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.Collapsible, {\n                                            open: expanded.events,\n                                            onOpenChange: ()=>toggleExpanded('events'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 802,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Event ID Validation Issues (\",\n                                                                        (validationCounts === null || validationCounts === void 0 ? void 0 : validationCounts.eventCount) || 0,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        expanded.events ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-4 flex flex-col sm:flex-row gap-2 justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"flex items-center\",\n                                                                    disabled: isSyncing || isFixingImages,\n                                                                    onClick: async ()=>{\n                                                                        try {\n                                                                            setIsFixingImages(true);\n                                                                            const token = await getAccessToken();\n                                                                            const response = await fetch(\"\".concat((0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)(), \"/admin/events/sync/fix-event-images\"), {\n                                                                                method: 'POST',\n                                                                                headers: {\n                                                                                    'Content-Type': 'application/json',\n                                                                                    'Authorization': \"Bearer \".concat(token)\n                                                                                }\n                                                                            });\n                                                                            if (!response.ok) {\n                                                                                throw new Error('Failed to fix event images');\n                                                                            }\n                                                                            const result = await response.json();\n                                                                            // Store the image fix results for display\n                                                                            setImageFixResults({\n                                                                                total: result.total || 0,\n                                                                                processed: result.processed || 0,\n                                                                                success: result.success || 0,\n                                                                                skipped: result.skipped || 0,\n                                                                                errors: result.errors || 0,\n                                                                                timestamp: new Date().toLocaleString()\n                                                                            });\n                                                                            toast({\n                                                                                title: \"Image Fix Complete\",\n                                                                                description: \"Fixed \".concat(result.success, \" event images out of \").concat(result.total, \" events checked.\")\n                                                                            });\n                                                                            // Refresh the validation issues after fixing images\n                                                                            refreshData();\n                                                                        } catch (error) {\n                                                                            console.error('Error fixing event images:', error);\n                                                                            toast({\n                                                                                title: \"Image Fix Failed\",\n                                                                                description: \"Failed to fix event images. See console for details.\",\n                                                                                variant: \"destructive\"\n                                                                            });\n                                                                        } finally{\n                                                                            setIsFixingImages(false);\n                                                                            setLoadingIssues(false);\n                                                                        }\n                                                                    },\n                                                                    children: isFixingImages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 870,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Processing...\"\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 875,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Fix Event Images\"\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 815,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"flex items-center\",\n                                                                    disabled: isSyncing || isFixingImages,\n                                                                    onClick: async ()=>{\n                                                                        try {\n                                                                            var _result_details;\n                                                                            setIsSyncing(true);\n                                                                            const token = await getAccessToken();\n                                                                            const response = await fetch(\"\".concat((0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)(), \"/admin/events/sync/fix-event-datetimes\"), {\n                                                                                method: 'POST',\n                                                                                headers: {\n                                                                                    'Authorization': \"Bearer \".concat(token),\n                                                                                    'Content-Type': 'application/json'\n                                                                                }\n                                                                            });\n                                                                            if (!response.ok) {\n                                                                                throw new Error('Failed to sync event date/times');\n                                                                            }\n                                                                            const result = await response.json();\n                                                                            // Store the sync results for display\n                                                                            setSyncResults({\n                                                                                eventsChecked: result.eventsFound || 0,\n                                                                                eventsFixed: result.eventsImported || 0,\n                                                                                eventsSkipped: result.eventsSkipped || 0,\n                                                                                fixedEvents: ((_result_details = result.details) === null || _result_details === void 0 ? void 0 : _result_details.updatedEvents) || [],\n                                                                                timestamp: new Date().toLocaleString()\n                                                                            });\n                                                                            toast({\n                                                                                title: \"Date/Time Sync Complete\",\n                                                                                description: \"Fixed \".concat(result.eventsImported, \" event datetime issues out of \").concat(result.eventsFound, \" events checked.\")\n                                                                            });\n                                                                            // Refresh the validation issues after syncing\n                                                                            refreshData();\n                                                                        } catch (error) {\n                                                                            console.error('Error syncing event date/times:', error);\n                                                                            toast({\n                                                                                title: \"Sync Failed\",\n                                                                                description: \"Failed to sync event date/times. See console for details.\",\n                                                                                variant: \"destructive\"\n                                                                            });\n                                                                        } finally{\n                                                                            setIsSyncing(false);\n                                                                            setLoadingIssues(false);\n                                                                        }\n                                                                    },\n                                                                    children: isSyncing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 935,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Syncing...\"\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 940,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Sync Date/Times\"\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 881,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        loadingIssues ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"py-4 text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-5 w-5 animate-spin mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 949,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Loading event issues...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 950,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 948,\n                                                            columnNumber: 25\n                                                        }, this) : issuesByType.events && issuesByType.events.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHeader, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                                children: \"Name\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 956,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                                children: \"Venue\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 957,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                                children: \"Start Date\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 958,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                                children: \"PostgreSQL ID\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 959,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                                children: \"Issue\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 960,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                                children: \"Actions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 961,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                        lineNumber: 955,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 954,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                colSpan: 6,\n                                                                                className: \"text-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        size: \"sm\",\n                                                                                        className: \"flex items-center mx-auto mt-2\",\n                                                                                        disabled: isBatchFixing || ((_issuesByType_events = issuesByType.events) === null || _issuesByType_events === void 0 ? void 0 : _issuesByType_events.filter((issue)=>issue.issueType === 'missing_postgres').length) === 0,\n                                                                                        onClick: async ()=>{\n                                                                                            try {\n                                                                                                setIsBatchFixing(true);\n                                                                                                const token = await getAccessToken();\n                                                                                                const response = await fetch(\"\".concat((0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)(), \"/admin/id-validation/batch-fix-events\"), {\n                                                                                                    method: 'POST',\n                                                                                                    headers: {\n                                                                                                        'Content-Type': 'application/json',\n                                                                                                        'Authorization': \"Bearer \".concat(token)\n                                                                                                    }\n                                                                                                });\n                                                                                                if (!response.ok) {\n                                                                                                    throw new Error('Failed to batch fix missing events');\n                                                                                                }\n                                                                                                const result = await response.json();\n                                                                                                // Store the batch fix results for display\n                                                                                                setBatchFixResults({\n                                                                                                    totalIssues: result.totalIssues || 0,\n                                                                                                    successCount: result.successCount || 0,\n                                                                                                    errorCount: result.errorCount || 0,\n                                                                                                    fixedEvents: result.fixedEvents || [],\n                                                                                                    errors: result.errors || [],\n                                                                                                    timestamp: new Date().toLocaleString()\n                                                                                                });\n                                                                                                toast({\n                                                                                                    title: \"Batch Fix Complete\",\n                                                                                                    description: \"Fixed \".concat(result.successCount, \" missing events out of \").concat(result.totalIssues, \" issues.\")\n                                                                                                });\n                                                                                                // Refresh the validation issues after batch fixing\n                                                                                                refreshData();\n                                                                                            } catch (error) {\n                                                                                                console.error('Error batch fixing events:', error);\n                                                                                                toast({\n                                                                                                    title: \"Batch Fix Failed\",\n                                                                                                    description: \"Failed to batch fix missing events. See console for details.\",\n                                                                                                    variant: \"destructive\"\n                                                                                                });\n                                                                                            } finally{\n                                                                                                setIsBatchFixing(false);\n                                                                                                setLoadingIssues(false);\n                                                                                            }\n                                                                                        },\n                                                                                        children: isBatchFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                    lineNumber: 1024,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                \"Fixing...\"\n                                                                                            ]\n                                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                    lineNumber: 1029,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                \"Batch Fix Missing Events\"\n                                                                                            ]\n                                                                                        }, void 0, true)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 969,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    syncResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"mt-4 p-3 bg-muted/50 rounded-md text-sm\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between mb-2\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                        className: \"font-medium flex items-center\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                                                className: \"h-4 w-4 mr-1\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1042,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            \"Sync Results\"\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1041,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"text-xs text-muted-foreground\",\n                                                                                                        children: syncResults.timestamp\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1045,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                lineNumber: 1040,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"grid grid-cols-3 gap-2 mb-3\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-background p-2 rounded\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                                children: \"Checked\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1052,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"font-medium\",\n                                                                                                                children: syncResults.eventsChecked\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1053,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1051,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-background p-2 rounded\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                                children: \"Fixed\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1056,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"font-medium text-green-600\",\n                                                                                                                children: syncResults.eventsFixed\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1057,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1055,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-background p-2 rounded\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                                children: \"Skipped\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1060,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"font-medium\",\n                                                                                                                children: syncResults.eventsSkipped\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1061,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1059,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                lineNumber: 1050,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            syncResults.fixedEvents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs font-medium mb-1\",\n                                                                                                        children: \"Fixed Events:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1067,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                                                                                                        className: \"h-[100px] w-full rounded border p-2 bg-background\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                            className: \"text-xs space-y-1\",\n                                                                                                            children: syncResults.fixedEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                                    className: \"text-muted-foreground\",\n                                                                                                                    children: [\n                                                                                                                        \"• \",\n                                                                                                                        event\n                                                                                                                    ]\n                                                                                                                }, index, true, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                    lineNumber: 1071,\n                                                                                                                    columnNumber: 47\n                                                                                                                }, this))\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                            lineNumber: 1069,\n                                                                                                            columnNumber: 43\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1068,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1039,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    batchFixResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"mt-4 p-3 bg-muted/50 rounded-md text-sm\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between mb-2\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                        className: \"font-medium flex items-center\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                                                className: \"h-4 w-4 mr-1\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1087,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            \"Batch Fix Results\"\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1086,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"text-xs text-muted-foreground\",\n                                                                                                        children: batchFixResults.timestamp\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1090,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                lineNumber: 1085,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"grid grid-cols-3 gap-2 mb-3\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-background p-2 rounded\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                                children: \"Total Issues\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1097,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"font-medium\",\n                                                                                                                children: batchFixResults.totalIssues\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1098,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1096,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-background p-2 rounded\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                                children: \"Fixed\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1101,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"font-medium text-green-600\",\n                                                                                                                children: batchFixResults.successCount\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1102,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1100,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-background p-2 rounded\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                                children: \"Errors\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1105,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"font-medium text-red-600\",\n                                                                                                                children: batchFixResults.errorCount\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1106,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1104,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                lineNumber: 1095,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            batchFixResults.fixedEvents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs font-medium mb-1\",\n                                                                                                        children: \"Fixed Events:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1112,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                                                                                                        className: \"h-[100px] w-full rounded border p-2 bg-background\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                            className: \"text-xs space-y-1\",\n                                                                                                            children: batchFixResults.fixedEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                                    className: \"text-muted-foreground\",\n                                                                                                                    children: [\n                                                                                                                        \"• \",\n                                                                                                                        event.name,\n                                                                                                                        \" (ID: \",\n                                                                                                                        event.id,\n                                                                                                                        \")\"\n                                                                                                                    ]\n                                                                                                                }, index, true, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                    lineNumber: 1116,\n                                                                                                                    columnNumber: 47\n                                                                                                                }, this))\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                            lineNumber: 1114,\n                                                                                                            columnNumber: 43\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1113,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true),\n                                                                                            batchFixResults.errors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs font-medium mb-1 mt-3 text-red-600\",\n                                                                                                        children: \"Errors:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1127,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                                                                                                        className: \"h-[100px] w-full rounded border p-2 bg-background\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                            className: \"text-xs space-y-1\",\n                                                                                                            children: batchFixResults.errors.map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                                    className: \"text-red-500\",\n                                                                                                                    children: [\n                                                                                                                        \"• \",\n                                                                                                                        error.event,\n                                                                                                                        \": \",\n                                                                                                                        error.error\n                                                                                                                    ]\n                                                                                                                }, index, true, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                    lineNumber: 1131,\n                                                                                                                    columnNumber: 47\n                                                                                                                }, this))\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                            lineNumber: 1129,\n                                                                                                            columnNumber: 43\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1128,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1084,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 967,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 966,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        issuesByType.events.map((issue)=>{\n                                                                            var _issue_postgresRecord, _issue_azureRecord, _issue_postgresRecord1, _issue_azureRecord1, _issue_postgresRecord2;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                        className: \"font-medium\",\n                                                                                        children: ((_issue_postgresRecord = issue.postgresRecord) === null || _issue_postgresRecord === void 0 ? void 0 : _issue_postgresRecord.name) || ((_issue_azureRecord = issue.azureRecord) === null || _issue_azureRecord === void 0 ? void 0 : _issue_azureRecord.name) || 'Unknown Event'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1145,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                        children: ((_issue_postgresRecord1 = issue.postgresRecord) === null || _issue_postgresRecord1 === void 0 ? void 0 : _issue_postgresRecord1.venue_name) || ((_issue_azureRecord1 = issue.azureRecord) === null || _issue_azureRecord1 === void 0 ? void 0 : _issue_azureRecord1.venue_name) || 'Unknown Venue'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1146,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                        children: (()=>{\n                                                                                            var _issue_postgresRecord, _issue_azureRecord;\n                                                                                            const dateTime = ((_issue_postgresRecord = issue.postgresRecord) === null || _issue_postgresRecord === void 0 ? void 0 : _issue_postgresRecord.startDateTime) || ((_issue_azureRecord = issue.azureRecord) === null || _issue_azureRecord === void 0 ? void 0 : _issue_azureRecord.startDateTime);\n                                                                                            if (!dateTime) return 'Unknown';\n                                                                                            // MSSQL datetime2 format: \"2025-10-05 00:00:00.0000000\"\n                                                                                            // Extract just the date portion (YYYY-MM-DD) without any timezone conversion\n                                                                                            if (dateTime.includes(' ')) {\n                                                                                                return dateTime.split(' ')[0];\n                                                                                            }\n                                                                                            // Handle ISO format (2025-12-12T00:00:00.000Z) - extract date only\n                                                                                            if (dateTime.includes('T')) {\n                                                                                                return dateTime.split('T')[0];\n                                                                                            }\n                                                                                            // Already just a date\n                                                                                            return dateTime;\n                                                                                        })()\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1147,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                        children: ((_issue_postgresRecord2 = issue.postgresRecord) === null || _issue_postgresRecord2 === void 0 ? void 0 : _issue_postgresRecord2.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                                    className: \"bg-secondary/50 px-1 py-0.5 rounded text-xs\",\n                                                                                                    children: [\n                                                                                                        issue.postgresRecord.id.substring(0, 8),\n                                                                                                        \"...\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                    lineNumber: 1168,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                    variant: \"ghost\",\n                                                                                                    size: \"icon\",\n                                                                                                    className: \"h-6 w-6 ml-1\",\n                                                                                                    onClick: ()=>{\n                                                                                                        navigator.clipboard.writeText(issue.postgresRecord.id);\n                                                                                                        toast({\n                                                                                                            title: \"Copied to clipboard\",\n                                                                                                            description: \"The PostgreSQL ID has been copied to your clipboard.\"\n                                                                                                        });\n                                                                                                    },\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                                        className: \"h-3 w-3\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1183,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                    lineNumber: 1171,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-muted-foreground text-xs italic\",\n                                                                                            children: \"Missing\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1187,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1165,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                                            children: issue.issueType === 'missing_azure' ? 'missing azure' : issue.issueType.replace('_', ' ')\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1191,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1190,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex space-x-2\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                variant: \"outline\",\n                                                                                                size: \"sm\",\n                                                                                                onClick: ()=>handleFixIssue(issue),\n                                                                                                children: \"Fix\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                lineNumber: 1197,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1196,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1195,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, issue.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 1144,\n                                                                                columnNumber: 31\n                                                                            }, this);\n                                                                        })\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 964,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"py-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground mb-4\",\n                                                                    children: \"No event ID validation issues found\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 1212,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        className: \"flex items-center mx-auto\",\n                                                                        disabled: isSyncing || isFixingImages,\n                                                                        onClick: async ()=>{\n                                                                            try {\n                                                                                var _result_details;\n                                                                                setIsSyncing(true);\n                                                                                const token = await getAccessToken();\n                                                                                const response = await fetch(\"\".concat((0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)(), \"/admin/events/sync/fix-event-datetimes\"), {\n                                                                                    method: 'POST',\n                                                                                    headers: {\n                                                                                        'Content-Type': 'application/json',\n                                                                                        'Authorization': \"Bearer \".concat(token)\n                                                                                    }\n                                                                                });\n                                                                                if (!response.ok) {\n                                                                                    throw new Error('Failed to sync event date/times');\n                                                                                }\n                                                                                const result = await response.json();\n                                                                                // Store the sync results for display\n                                                                                setSyncResults({\n                                                                                    eventsChecked: result.eventsFound || 0,\n                                                                                    eventsFixed: result.eventsImported || 0,\n                                                                                    eventsSkipped: result.eventsSkipped || 0,\n                                                                                    fixedEvents: ((_result_details = result.details) === null || _result_details === void 0 ? void 0 : _result_details.updatedEvents) || [],\n                                                                                    timestamp: new Date().toLocaleString()\n                                                                                });\n                                                                                toast({\n                                                                                    title: \"Date/Time Sync Complete\",\n                                                                                    description: \"Fixed \".concat(result.eventsImported, \" event datetime issues out of \").concat(result.eventsFound, \" events checked.\")\n                                                                                });\n                                                                                // Refresh the validation issues after syncing\n                                                                                refreshData();\n                                                                            } catch (error) {\n                                                                                console.error('Error syncing event date/times:', error);\n                                                                                toast({\n                                                                                    title: \"Sync Failed\",\n                                                                                    description: \"Failed to sync event date/times. See console for details.\",\n                                                                                    variant: \"destructive\"\n                                                                                });\n                                                                            } finally{\n                                                                                setIsSyncing(false);\n                                                                                setLoadingIssues(false);\n                                                                            }\n                                                                        },\n                                                                        children: isSyncing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1268,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"Syncing...\"\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1273,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"Sync Date/Times\"\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                        lineNumber: 1214,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 1213,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                syncResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-4 p-3 bg-muted/50 rounded-md text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-medium flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                            className: \"h-4 w-4 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1285,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        \"Date/Time Sync Results\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1284,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: syncResults.timestamp\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1288,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 1283,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-2 mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Checked\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1295,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: syncResults.eventsChecked\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1296,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1294,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Fixed\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1299,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium text-green-600\",\n                                                                                            children: syncResults.eventsFixed\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1300,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1298,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Skipped\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1303,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: syncResults.eventsSkipped\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1304,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1302,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 1293,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        syncResults.fixedEvents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs font-medium mb-1\",\n                                                                                    children: \"Fixed Events:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1310,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                                                                                    className: \"h-[100px] w-full rounded border p-2 bg-background\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                        className: \"text-xs space-y-1\",\n                                                                                        children: syncResults.fixedEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                className: \"text-muted-foreground\",\n                                                                                                children: [\n                                                                                                    \"• \",\n                                                                                                    event\n                                                                                                ]\n                                                                                            }, index, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                lineNumber: 1314,\n                                                                                                columnNumber: 41\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1312,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1311,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 1282,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                imageFixResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-4 p-3 bg-muted/50 rounded-md text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-medium flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            className: \"h-4 w-4 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1330,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        \"Image Fix Results\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1329,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: imageFixResults.timestamp\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1333,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 1328,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-4 gap-2 mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Total\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1340,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: imageFixResults.total\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1341,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1339,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Processed\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1344,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: imageFixResults.processed\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1345,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1343,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Success\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1348,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium text-green-600\",\n                                                                                            children: imageFixResults.success\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1349,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1347,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Errors\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1352,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium text-red-600\",\n                                                                                            children: imageFixResults.errors\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1353,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1351,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 1338,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 1327,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 1211,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                            lineNumber: 795,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 15\n                            }, this),\n                            selectedIssue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fix_dialog__WEBPACK_IMPORTED_MODULE_12__.FixDialog, {\n                                        open: fixDialogOpen,\n                                        setOpen: setFixDialogOpen,\n                                        issue: selectedIssue,\n                                        onFix: async (issue, matchId)=>{\n                                            // Check if the issue has the alreadyHandled flag\n                                            if (issue && 'alreadyHandled' in issue && issue.alreadyHandled === true) {\n                                                // Just refresh the data without calling handleResolveIssue\n                                                await refreshData();\n                                                return;\n                                            }\n                                            // If no matchId is provided and no azureId in the issue, just refresh\n                                            if (!matchId && (!issue || !('azureId' in issue))) {\n                                                await refreshData();\n                                                return;\n                                            }\n                                            // Otherwise, proceed with the normal flow\n                                            await handleResolveIssue((issue === null || issue === void 0 ? void 0 : issue.id) || (selectedIssue === null || selectedIssue === void 0 ? void 0 : selectedIssue.id) || '', 'fix', 'azureId' in issue ? {\n                                                azureId: issue.azureId\n                                            } : matchId || selectedMatchId);\n                                        },\n                                        matchId: selectedMatchId,\n                                        setMatchId: setSelectedMatchId\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                        lineNumber: 1367,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_merge_dialog__WEBPACK_IMPORTED_MODULE_11__.MergeDialog, {\n                                        open: mergeDialogOpen,\n                                        setOpen: setMergeDialogOpen,\n                                        type: selectedIssue.entityType,\n                                        duplicateGroup: {\n                                            ids: [\n                                                selectedIssue.id\n                                            ],\n                                            recordMetadata: [\n                                                {\n                                                    id: selectedIssue.id,\n                                                    name: ((_selectedIssue_postgresRecord = selectedIssue.postgresRecord) === null || _selectedIssue_postgresRecord === void 0 ? void 0 : _selectedIssue_postgresRecord.name) || ((_selectedIssue_azureRecord = selectedIssue.azureRecord) === null || _selectedIssue_azureRecord === void 0 ? void 0 : _selectedIssue_azureRecord.name) || 'Unknown',\n                                                    createdAt: ((_selectedIssue_postgresRecord1 = selectedIssue.postgresRecord) === null || _selectedIssue_postgresRecord1 === void 0 ? void 0 : _selectedIssue_postgresRecord1.createdAt) || ((_selectedIssue_azureRecord1 = selectedIssue.azureRecord) === null || _selectedIssue_azureRecord1 === void 0 ? void 0 : _selectedIssue_azureRecord1.createdAt) || new Date().toISOString(),\n                                                    updatedAt: ((_selectedIssue_postgresRecord2 = selectedIssue.postgresRecord) === null || _selectedIssue_postgresRecord2 === void 0 ? void 0 : _selectedIssue_postgresRecord2.updatedAt) || ((_selectedIssue_azureRecord2 = selectedIssue.azureRecord) === null || _selectedIssue_azureRecord2 === void 0 ? void 0 : _selectedIssue_azureRecord2.updatedAt) || new Date().toISOString()\n                                                }\n                                            ]\n                                        },\n                                        onMerge: (primaryId, duplicateIds)=>{\n                                            handleResolveIssue(selectedIssue.id, \"merge\", primaryId);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                        lineNumber: 1396,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                lineNumber: 507,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n        lineNumber: 495,\n        columnNumber: 5\n    }, this);\n}\n_s(IdValidation, \"5p7sltQSwLWSfzkctYpSiHipCQk=\", false, function() {\n    return [\n        _hooks_use_auth__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter\n    ];\n});\n_c = IdValidation;\nvar _c;\n$RefreshReg$(_c, \"IdValidation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/id-validation/index.tsx\n"));

/***/ })

});