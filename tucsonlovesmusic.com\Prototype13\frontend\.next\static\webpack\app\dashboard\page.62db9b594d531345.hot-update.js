"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/admin/event-relations/venue.tsx":
/*!****************************************************!*\
  !*** ./components/admin/event-relations/venue.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventVenueRelations: () => (/* binding */ EventVenueRelations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-auth */ \"(app-pages-browser)/./hooks/use-auth.ts\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ EventVenueRelations auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction EventVenueRelations() {\n    _s();\n    const [counts, setCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [missingEvents, setMissingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mismatchedEvents, setMismatchedEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isBulkFixing, setIsBulkFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [batchFixing, setBatchFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [csvFixing, setCsvFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [batchFixProgress, setBatchFixProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 0,\n        total: 0,\n        success: 0,\n        failed: 0\n    });\n    const [fixingEvents, setFixingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        missing: false,\n        mismatched: false\n    });\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { getAccessToken } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // Fetch data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventVenueRelations.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"EventVenueRelations.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            setIsLoading(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Fetch counts\n            const countsResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!countsResponse.ok) {\n                throw new Error('Failed to fetch event-venue relationship counts');\n            }\n            const countsData = await countsResponse.json();\n            setCounts(countsData);\n            // Fetch missing events\n            const missingResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/missing\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (missingResponse.ok) {\n                const missingData = await missingResponse.json();\n                setMissingEvents(missingData);\n            }\n            // Fetch mismatched events\n            const mismatchedResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/mismatched\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (mismatchedResponse.ok) {\n                const mismatchedData = await mismatchedResponse.json();\n                setMismatchedEvents(mismatchedData);\n            }\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching event-venue relationship data', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch event-venue relationship data');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleExpanded = (section)=>{\n        setExpanded((prev)=>({\n                ...prev,\n                [section]: !prev[section]\n            }));\n    };\n    const handleFixAll = async ()=>{\n        try {\n            setIsBulkFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix-all\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fix all event-venue relationships');\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: result.message,\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing all event-venue relationships', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to fix all event-venue relationships',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsBulkFixing(false);\n        }\n    };\n    // Handle batch fixing a limited number of events\n    const handleBatchFix = async ()=>{\n        try {\n            setBatchFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Determine which events to fix - prioritize missing events\n            const eventsToFix = [\n                ...missingEvents\n            ].slice(0, 10); // Limit to 10 events at a time\n            if (eventsToFix.length === 0) {\n                toast({\n                    title: 'No Events to Fix',\n                    description: 'There are no events that need fixing.',\n                    variant: 'default'\n                });\n                return;\n            }\n            // Set up progress tracking\n            setBatchFixProgress({\n                current: 0,\n                total: eventsToFix.length,\n                success: 0,\n                failed: 0\n            });\n            // Mark all events as being fixed\n            const newFixingState = {};\n            eventsToFix.forEach((event)=>{\n                newFixingState[event.id] = true;\n            });\n            setFixingEvents((prev)=>({\n                    ...prev,\n                    ...newFixingState\n                }));\n            console.log(\"\\uD83D\\uDD27 [Frontend] Starting batch fix for \".concat(eventsToFix.length, \" venue relationships\"));\n            // Process events sequentially to avoid overwhelming the server\n            for(let i = 0; i < eventsToFix.length; i++){\n                const event = eventsToFix[i];\n                setBatchFixProgress((prev)=>({\n                        ...prev,\n                        current: i + 1\n                    }));\n                try {\n                    console.log(\"\\uD83D\\uDD27 [Frontend] Fixing event \".concat(i + 1, \"/\").concat(eventsToFix.length, \": \").concat(event.name, \" (ID: \").concat(event.id, \")\"));\n                    // Create the request body with eventId and venueId\n                    const requestBody = {\n                        eventId: event.id,\n                        venueId: event.azure_venue_id // Using the Azure venue ID from the selected event\n                    };\n                    const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix\"), {\n                        method: 'POST',\n                        headers: {\n                            'Authorization': \"Bearer \".concat(token),\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify(requestBody)\n                    });\n                    if (!response.ok) {\n                        console.error(\"\\uD83D\\uDD27 [Frontend] Failed to fix event \".concat(event.name, \": \").concat(response.status));\n                        setBatchFixProgress((prev)=>({\n                                ...prev,\n                                failed: prev.failed + 1\n                            }));\n                    } else {\n                        console.log(\"\\uD83D\\uDD27 [Frontend] Successfully fixed event \".concat(event.name));\n                        setBatchFixProgress((prev)=>({\n                                ...prev,\n                                success: prev.success + 1\n                            }));\n                    }\n                } catch (eventError) {\n                    console.error(\"\\uD83D\\uDD27 [Frontend] Error fixing event \".concat(event.name, \":\"), eventError);\n                    setBatchFixProgress((prev)=>({\n                            ...prev,\n                            failed: prev.failed + 1\n                        }));\n                } finally{\n                    // Mark this event as no longer being fixed\n                    setFixingEvents((prev)=>({\n                            ...prev,\n                            [event.id]: false\n                        }));\n                }\n                // Small delay to avoid overwhelming the server\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n            }\n            toast({\n                title: 'Batch Fix Complete',\n                description: \"Successfully fixed \".concat(batchFixProgress.success, \" out of \").concat(eventsToFix.length, \" venue relationships.\"),\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error during batch fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete batch fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setBatchFixing(false);\n            // Reset progress\n            setBatchFixProgress({\n                current: 0,\n                total: 0,\n                success: 0,\n                failed: 0\n            });\n        }\n    };\n    // Handle CSV-based fix for all event-venue relationships\n    const handleCsvFix = async ()=>{\n        try {\n            var _result_stats;\n            setCsvFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            console.log('🔧 [Frontend] Starting CSV-based fix for event-venue relationships');\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix-from-csv\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('🔧 [Frontend] Server error response:', errorText);\n                throw new Error(\"Failed to execute CSV fix: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('🔧 [Frontend] CSV fix completed:', result);\n            toast({\n                title: 'CSV Fix Complete',\n                description: \"Validated \".concat(((_result_stats = result.stats) === null || _result_stats === void 0 ? void 0 : _result_stats.validatedRelationships) || 0, \" venue relationships from CSV\"),\n                variant: 'default'\n            });\n            // Refresh data after CSV fix\n            fetchData();\n        } catch (err) {\n            console.error('🔧 [Frontend] Error during CSV fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete CSV fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setCsvFixing(false);\n        }\n    };\n    // Open confirmation dialog for fixing an event\n    const openFixConfirmDialog = (event)=>{\n        setSelectedEvent(event);\n        setConfirmDialogOpen(true);\n    };\n    // Handle fixing a single event's venue relationship through the new confirmation dialog\n    const handleFixEventVenue = async ()=>{\n        if (!selectedEvent) return;\n        try {\n            // Close the dialog\n            setConfirmDialogOpen(false);\n            // Mark this event as being fixed\n            setFixingEvents((prev)=>({\n                    ...prev,\n                    [selectedEvent.id]: true\n                }));\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Create the request body with eventId and venueId\n            const requestBody = {\n                eventId: selectedEvent.id,\n                venueId: selectedEvent.azure_venue_id // Using the Azure venue ID from the selected event\n            };\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to fix event-venue relationship for \".concat(selectedEvent.name));\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: 'Event \"'.concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name, '\" venue updated to \"').concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.azure_venue_name, '\"'),\n                variant: 'default'\n            });\n            // Refresh data after successful fix\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing event-venue relationship', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : \"Failed to fix event-venue relationship for \".concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name),\n                variant: 'destructive'\n            });\n        } finally{\n            // Clear the fixing state for this event\n            if (selectedEvent) {\n                setFixingEvents((prev)=>({\n                        ...prev,\n                        [selectedEvent.id]: false\n                    }));\n            }\n            // Clear the selected event\n            setSelectedEvent(null);\n        }\n    };\n    // Original function that was used with the FixVenueDialog component\n    // Now adapted to work with our centralized confirmation dialog\n    async function handleFixEventVenue_legacy(eventId, azureVenueId) {\n        try {\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    eventId,\n                    venueId: azureVenueId\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fix event-venue relationship');\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: 'Event venue relationship fixed successfully',\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing event-venue relationship', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to fix event-venue relationship',\n                variant: 'destructive'\n            });\n        }\n    }\n    // Determine status message and icon color\n    const getStatusDetails = ()=>{\n        if (!counts) return {\n            message: 'Loading...',\n            status: 'neutral'\n        };\n        // Check if there are any actual problematic relationships in the dropdown lists\n        const hasProblematicRelations = missingEvents.length > 0 || mismatchedEvents.length > 0;\n        // If there are no problematic relations to troubleshoot, show green\n        // Only show yellow warning when there's something that needs attention\n        if (!hasProblematicRelations) {\n            return {\n                message: counts.missingVenues === 0 ? 'All events have venue relationships' : \"\".concat(counts.missingVenues, \" events with no venue relations\"),\n                status: 'good'\n            };\n        }\n        // Show yellow warning when there are items in the dropdown lists that need attention\n        return {\n            message: \"\".concat(counts.missingVenues, \" events with no venue relations\"),\n            status: 'warning'\n        };\n    };\n    // Get the appropriate icon color based on status\n    const getIconColorClass = (status)=>{\n        switch(status){\n            case 'good':\n                return 'text-green-500';\n            case 'warning':\n                return 'text-yellow-500';\n            case 'bad':\n                return 'text-red-500';\n            default:\n                return 'text-muted-foreground';\n        }\n    };\n    const statusDetails = getStatusDetails();\n    // Format date for display\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'No date';\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"flex flex-row items-center space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-5 w-5 mr-2 \".concat(getIconColorClass(statusDetails.status))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-xl\",\n                            children: \"Events - Venues\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-8 w-8 animate-spin text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-muted-foreground\",\n                                children: \"Loading relationship data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 11\n                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8 px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5 text-red-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-500\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: statusDetails.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            onClick: handleBatchFix,\n                                            disabled: batchFixing || missingEvents.length === 0,\n                                            className: \"flex items-center\",\n                                            variant: \"outline\",\n                                            children: batchFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Fixing \",\n                                                    batchFixProgress.current,\n                                                    \"/\",\n                                                    batchFixProgress.total,\n                                                    \"...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Batch Fix (10 Events)\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"icon\",\n                                            onClick: fetchData,\n                                            disabled: isLoading || batchFixing,\n                                            title: \"Refresh data\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 15\n                                }, this),\n                                batchFixing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-muted rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm mb-1 flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Batch Fix Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        batchFixProgress.current,\n                                                        \"/\",\n                                                        batchFixProgress.total,\n                                                        \" events\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-secondary h-2 rounded-full overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary h-full transition-all duration-300 ease-in-out\",\n                                                style: {\n                                                    width: \"\".concat(batchFixProgress.current / Math.max(batchFixProgress.total, 1) * 100, \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between text-xs mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500\",\n                                                    children: [\n                                                        batchFixProgress.success,\n                                                        \" successful\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: [\n                                                        batchFixProgress.failed,\n                                                        \" failed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                            open: expanded.missing,\n                                            onOpenChange: ()=>toggleExpanded('missing'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Events Missing Venue Relationships (\",\n                                                                        missingEvents.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 620,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        expanded.missing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 625,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: missingEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No events missing venues\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Event Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 636,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 637,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Azure Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 638,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"PostgreSQL Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 639,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 640,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                                children: missingEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"font-medium\",\n                                                                                children: event.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 646,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: formatDate(event.date)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 647,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: event.azure_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 648,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"text-red-500\",\n                                                                                children: event.postgres_venue_name || 'Missing'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 649,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>openFixConfirmDialog(event),\n                                                                                    disabled: fixingEvents[event.id],\n                                                                                    children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                                lineNumber: 659,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            \"Fixing...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Fix\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                    lineNumber: 651,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 650,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, event.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 645,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                            open: expanded.mismatched,\n                                            onOpenChange: ()=>toggleExpanded('mismatched'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Events with Mismatched Venue (\",\n                                                                        mismatchedEvents.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        expanded.mismatched ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 689,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: mismatchedEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No events with mismatched venues\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Event Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 700,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 701,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Azure Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 702,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"PostgreSQL Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 703,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 704,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 698,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                                children: mismatchedEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"font-medium\",\n                                                                                children: event.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 710,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: formatDate(event.date)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 711,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: event.azure_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 712,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"text-yellow-500\",\n                                                                                children: event.postgres_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 713,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>openFixConfirmDialog(event),\n                                                                                    disabled: fixingEvents[event.id],\n                                                                                    children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                                lineNumber: 723,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            \"Fixing...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Fix\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                    lineNumber: 715,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 714,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, event.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 709,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 676,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                        open: confirmDialogOpen,\n                        onOpenChange: setConfirmDialogOpen,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                            children: \"Fix Event-Venue Relationship\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                                            children: selectedEvent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Are you sure you want to fix the venue relationship for this event?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-muted p-3 rounded-md text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Event:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Date:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 755,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formatDate((selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.date) || '')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Current Venue:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    (selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.postgres_venue_name) || 'None'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Target Venue:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 757,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.azure_venue_name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"This will:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc pl-5 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Update the event to reference the correct venue from Azure\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Create the venue in PostgreSQL if it doesn't exist\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Update the event's venue_id to maintain data consistency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setConfirmDialogOpen(false),\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 771,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            onClick: handleFixEventVenue,\n                                            children: \"Fix\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 770,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 745,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 744,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                lineNumber: 504,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n        lineNumber: 495,\n        columnNumber: 5\n    }, this);\n}\n_s(EventVenueRelations, \"vyrNPCcHdw+/JqHYqbEUKP4OiFA=\", false, function() {\n    return [\n        _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = EventVenueRelations;\nvar _c;\n$RefreshReg$(_c, \"EventVenueRelations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/event-relations/venue.tsx\n"));

/***/ })

});