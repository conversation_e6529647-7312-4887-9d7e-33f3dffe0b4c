"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/admin/event-relations/venue.tsx":
/*!****************************************************!*\
  !*** ./components/admin/event-relations/venue.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventVenueRelations: () => (/* binding */ EventVenueRelations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-auth */ \"(app-pages-browser)/./hooks/use-auth.ts\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ EventVenueRelations auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction EventVenueRelations() {\n    _s();\n    const [counts, setCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [missingEvents, setMissingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mismatchedEvents, setMismatchedEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isBulkFixing, setIsBulkFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [batchFixing, setBatchFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [csvFixing, setCsvFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [batchFixProgress, setBatchFixProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 0,\n        total: 0,\n        success: 0,\n        failed: 0\n    });\n    const [fixingEvents, setFixingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        missing: false,\n        mismatched: false\n    });\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { getAccessToken } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // Fetch data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventVenueRelations.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"EventVenueRelations.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            setIsLoading(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Fetch counts\n            const countsResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!countsResponse.ok) {\n                throw new Error('Failed to fetch event-venue relationship counts');\n            }\n            const countsData = await countsResponse.json();\n            setCounts(countsData);\n            // Fetch missing events\n            const missingResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/missing\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (missingResponse.ok) {\n                const missingData = await missingResponse.json();\n                setMissingEvents(missingData);\n            }\n            // Fetch mismatched events\n            const mismatchedResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/mismatched\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (mismatchedResponse.ok) {\n                const mismatchedData = await mismatchedResponse.json();\n                setMismatchedEvents(mismatchedData);\n            }\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching event-venue relationship data', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch event-venue relationship data');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleExpanded = (section)=>{\n        setExpanded((prev)=>({\n                ...prev,\n                [section]: !prev[section]\n            }));\n    };\n    const handleFixAll = async ()=>{\n        try {\n            setIsBulkFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/fix-all\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fix all event-venue relationships');\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: result.message,\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing all event-venue relationships', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to fix all event-venue relationships',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsBulkFixing(false);\n        }\n    };\n    // Handle batch fixing a limited number of events\n    const handleBatchFix = async ()=>{\n        try {\n            setBatchFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Determine which events to fix - prioritize missing events\n            const eventsToFix = [\n                ...missingEvents\n            ].slice(0, 10); // Limit to 10 events at a time\n            if (eventsToFix.length === 0) {\n                toast({\n                    title: 'No Events to Fix',\n                    description: 'There are no events that need fixing.',\n                    variant: 'default'\n                });\n                return;\n            }\n            // Set up progress tracking\n            setBatchFixProgress({\n                current: 0,\n                total: eventsToFix.length,\n                success: 0,\n                failed: 0\n            });\n            // Mark all events as being fixed\n            const newFixingState = {};\n            eventsToFix.forEach((event)=>{\n                newFixingState[event.id] = true;\n            });\n            setFixingEvents((prev)=>({\n                    ...prev,\n                    ...newFixingState\n                }));\n            console.log(\"\\uD83D\\uDD27 [Frontend] Starting batch fix for \".concat(eventsToFix.length, \" venue relationships\"));\n            // Process events sequentially to avoid overwhelming the server\n            for(let i = 0; i < eventsToFix.length; i++){\n                const event = eventsToFix[i];\n                setBatchFixProgress((prev)=>({\n                        ...prev,\n                        current: i + 1\n                    }));\n                try {\n                    console.log(\"\\uD83D\\uDD27 [Frontend] Fixing event \".concat(i + 1, \"/\").concat(eventsToFix.length, \": \").concat(event.name, \" (ID: \").concat(event.id, \")\"));\n                    // Create the request body with eventId and venueId\n                    const requestBody = {\n                        eventId: event.id,\n                        venueId: event.azure_venue_id // Using the Azure venue ID from the selected event\n                    };\n                    const response = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/fix\"), {\n                        method: 'POST',\n                        headers: {\n                            'Authorization': \"Bearer \".concat(token),\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify(requestBody)\n                    });\n                    if (!response.ok) {\n                        console.error(\"\\uD83D\\uDD27 [Frontend] Failed to fix event \".concat(event.name, \": \").concat(response.status));\n                        setBatchFixProgress((prev)=>({\n                                ...prev,\n                                failed: prev.failed + 1\n                            }));\n                    } else {\n                        console.log(\"\\uD83D\\uDD27 [Frontend] Successfully fixed event \".concat(event.name));\n                        setBatchFixProgress((prev)=>({\n                                ...prev,\n                                success: prev.success + 1\n                            }));\n                    }\n                } catch (eventError) {\n                    console.error(\"\\uD83D\\uDD27 [Frontend] Error fixing event \".concat(event.name, \":\"), eventError);\n                    setBatchFixProgress((prev)=>({\n                            ...prev,\n                            failed: prev.failed + 1\n                        }));\n                } finally{\n                    // Mark this event as no longer being fixed\n                    setFixingEvents((prev)=>({\n                            ...prev,\n                            [event.id]: false\n                        }));\n                }\n                // Small delay to avoid overwhelming the server\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n            }\n            toast({\n                title: 'Batch Fix Complete',\n                description: \"Successfully fixed \".concat(batchFixProgress.success, \" out of \").concat(eventsToFix.length, \" venue relationships.\"),\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error during batch fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete batch fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setBatchFixing(false);\n            // Reset progress\n            setBatchFixProgress({\n                current: 0,\n                total: 0,\n                success: 0,\n                failed: 0\n            });\n        }\n    };\n    // Open confirmation dialog for fixing an event\n    const openFixConfirmDialog = (event)=>{\n        setSelectedEvent(event);\n        setConfirmDialogOpen(true);\n    };\n    // Handle fixing a single event's venue relationship through the new confirmation dialog\n    const handleFixEventVenue = async ()=>{\n        if (!selectedEvent) return;\n        try {\n            // Close the dialog\n            setConfirmDialogOpen(false);\n            // Mark this event as being fixed\n            setFixingEvents((prev)=>({\n                    ...prev,\n                    [selectedEvent.id]: true\n                }));\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Create the request body with eventId and venueId\n            const requestBody = {\n                eventId: selectedEvent.id,\n                venueId: selectedEvent.azure_venue_id // Using the Azure venue ID from the selected event\n            };\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to fix event-venue relationship for \".concat(selectedEvent.name));\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: 'Event \"'.concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name, '\" venue updated to \"').concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.azure_venue_name, '\"'),\n                variant: 'default'\n            });\n            // Refresh data after successful fix\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing event-venue relationship', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : \"Failed to fix event-venue relationship for \".concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name),\n                variant: 'destructive'\n            });\n        } finally{\n            // Clear the fixing state for this event\n            if (selectedEvent) {\n                setFixingEvents((prev)=>({\n                        ...prev,\n                        [selectedEvent.id]: false\n                    }));\n            }\n            // Clear the selected event\n            setSelectedEvent(null);\n        }\n    };\n    // Original function that was used with the FixVenueDialog component\n    // Now adapted to work with our centralized confirmation dialog\n    async function handleFixEventVenue_legacy(eventId, azureVenueId) {\n        try {\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    eventId,\n                    venueId: azureVenueId\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fix event-venue relationship');\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: 'Event venue relationship fixed successfully',\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing event-venue relationship', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to fix event-venue relationship',\n                variant: 'destructive'\n            });\n        }\n    }\n    // Determine status message and icon color\n    const getStatusDetails = ()=>{\n        if (!counts) return {\n            message: 'Loading...',\n            status: 'neutral'\n        };\n        // Check if there are any actual problematic relationships in the dropdown lists\n        const hasProblematicRelations = missingEvents.length > 0 || mismatchedEvents.length > 0;\n        // If there are no problematic relations to troubleshoot, show green\n        // Only show yellow warning when there's something that needs attention\n        if (!hasProblematicRelations) {\n            return {\n                message: counts.missingVenues === 0 ? 'All events have venue relationships' : \"\".concat(counts.missingVenues, \" events with no venue relations\"),\n                status: 'good'\n            };\n        }\n        // Show yellow warning when there are items in the dropdown lists that need attention\n        return {\n            message: \"\".concat(counts.missingVenues, \" events with no venue relations\"),\n            status: 'warning'\n        };\n    };\n    // Get the appropriate icon color based on status\n    const getIconColorClass = (status)=>{\n        switch(status){\n            case 'good':\n                return 'text-green-500';\n            case 'warning':\n                return 'text-yellow-500';\n            case 'bad':\n                return 'text-red-500';\n            default:\n                return 'text-muted-foreground';\n        }\n    };\n    const statusDetails = getStatusDetails();\n    // Format date for display\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'No date';\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"flex flex-row items-center space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-5 w-5 mr-2 \".concat(getIconColorClass(statusDetails.status))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-xl\",\n                            children: \"Events - Venues\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-8 w-8 animate-spin text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-muted-foreground\",\n                                children: \"Loading relationship data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 11\n                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8 px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5 text-red-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-500\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: statusDetails.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            onClick: handleBatchFix,\n                                            disabled: batchFixing || missingEvents.length === 0,\n                                            className: \"flex items-center\",\n                                            variant: \"outline\",\n                                            children: batchFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Fixing \",\n                                                    batchFixProgress.current,\n                                                    \"/\",\n                                                    batchFixProgress.total,\n                                                    \"...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Batch Fix (10 Events)\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"icon\",\n                                            onClick: fetchData,\n                                            disabled: isLoading || batchFixing,\n                                            title: \"Refresh data\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 15\n                                }, this),\n                                batchFixing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-muted rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm mb-1 flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Batch Fix Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        batchFixProgress.current,\n                                                        \"/\",\n                                                        batchFixProgress.total,\n                                                        \" events\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-secondary h-2 rounded-full overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary h-full transition-all duration-300 ease-in-out\",\n                                                style: {\n                                                    width: \"\".concat(batchFixProgress.current / Math.max(batchFixProgress.total, 1) * 100, \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between text-xs mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500\",\n                                                    children: [\n                                                        batchFixProgress.success,\n                                                        \" successful\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: [\n                                                        batchFixProgress.failed,\n                                                        \" failed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                            open: expanded.missing,\n                                            onOpenChange: ()=>toggleExpanded('missing'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Events Missing Venue Relationships (\",\n                                                                        missingEvents.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        expanded.missing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: missingEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No events missing venues\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Event Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 590,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Azure Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 591,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"PostgreSQL Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 592,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 593,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                                children: missingEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"font-medium\",\n                                                                                children: event.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 599,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: formatDate(event.date)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 600,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: event.azure_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 601,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"text-red-500\",\n                                                                                children: event.postgres_venue_name || 'Missing'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 602,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>openFixConfirmDialog(event),\n                                                                                    disabled: fixingEvents[event.id],\n                                                                                    children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                                lineNumber: 612,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            \"Fixing...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Fix\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                    lineNumber: 604,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 603,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, event.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 598,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                            open: expanded.mismatched,\n                                            onOpenChange: ()=>toggleExpanded('mismatched'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 636,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Events with Mismatched Venue (\",\n                                                                        mismatchedEvents.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 635,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        expanded.mismatched ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: mismatchedEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No events with mismatched venues\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Event Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 653,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 654,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Azure Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 655,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"PostgreSQL Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 656,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 657,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 652,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 651,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                                children: mismatchedEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"font-medium\",\n                                                                                children: event.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 663,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: formatDate(event.date)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 664,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: event.azure_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 665,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"text-yellow-500\",\n                                                                                children: event.postgres_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 666,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>openFixConfirmDialog(event),\n                                                                                    disabled: fixingEvents[event.id],\n                                                                                    children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                                lineNumber: 676,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            \"Fixing...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Fix\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                    lineNumber: 668,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 667,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, event.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 662,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 660,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                        open: confirmDialogOpen,\n                        onOpenChange: setConfirmDialogOpen,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                            children: \"Fix Event-Venue Relationship\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                                            children: selectedEvent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Are you sure you want to fix the venue relationship for this event?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-muted p-3 rounded-md text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Event:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Date:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 708,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formatDate((selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.date) || '')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Current Venue:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 709,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    (selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.postgres_venue_name) || 'None'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Target Venue:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 710,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.azure_venue_name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"This will:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 713,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc pl-5 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Update the event to reference the correct venue from Azure\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 715,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Create the venue in PostgreSQL if it doesn't exist\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 716,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Update the event's venue_id to maintain data consistency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setConfirmDialogOpen(false),\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            onClick: handleFixEventVenue,\n                                            children: \"Fix\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 697,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                lineNumber: 457,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n        lineNumber: 448,\n        columnNumber: 5\n    }, this);\n}\n_s(EventVenueRelations, \"vyrNPCcHdw+/JqHYqbEUKP4OiFA=\", false, function() {\n    return [\n        _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = EventVenueRelations;\nvar _c;\n$RefreshReg$(_c, \"EventVenueRelations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/event-relations/venue.tsx\n"));

/***/ })

});