import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { EventSyncLog, EventSyncDetails } from '../entities/event-sync-log.entity';
import { Cron, CronExpression } from '@nestjs/schedule';
import { MigrationOrchestrator, MigrationSummary } from '../../database/migration-modules';
import { EventSyncResult } from '../../database/migrate-new-azure-events-modular';
import * as sql from 'mssql';

@Injectable()
export class EventSyncService {
    private readonly logger = new Logger(EventSyncService.name);
    private migrationOrchestrator: MigrationOrchestrator;

    constructor(
        @InjectRepository(EventSyncLog)
        private eventSyncLogRepository: Repository<EventSyncLog>,
        private dataSource: DataSource,
    ) {
        this.migrationOrchestrator = new MigrationOrchestrator(this.dataSource, {
            skipDuplicates: true,
            enableImageProcessing: true,
            enableFuzzyDuplicateDetection: true,
            dryRun: false
        });
    }

    @Cron(CronExpression.EVERY_12_HOURS)
    async runHourlySync() {
        try {
            this.logger.log('Starting scheduled 12-hourly event sync');
            
            // Check if another sync is already running
            const runningSync = await this.eventSyncLogRepository.findOne({
                where: { status: 'running' }
            });
            
            if (runningSync) {
                // If a sync has been running for more than 30 minutes, consider it stuck
                const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
                if (runningSync.startTime < thirtyMinutesAgo) {
                    this.logger.warn(`Found a stuck sync job from ${runningSync.startTime.toISOString()}. Marking as failed and starting new sync.`);
                    runningSync.status = 'failed';
                    runningSync.endTime = new Date();
                    runningSync.error = 'Sync job timed out after 30 minutes';
                    await this.eventSyncLogRepository.save(runningSync);
                } else {
                    this.logger.log(`Skipping 12-hourly sync as another sync job is already running since ${runningSync.startTime.toISOString()}`);
                    return;
                }
            }
            
            await this.triggerSync();
        } catch (error) {
            this.logger.error(`12-hourly event sync failed: ${error.message}`, error.stack);
        }
    }

    async getLatestSync() {
        const logs = await this.eventSyncLogRepository.find({
            order: { startTime: 'DESC' },
            take: 1
        });
        return logs.length > 0 ? logs[0] : null;
    }

    async getSyncHistory(limit: number = 10) {
        return this.eventSyncLogRepository.find({
            order: { startTime: 'DESC' },
            take: limit
        });
    }

    async triggerManualSync() {
        this.logger.log('Manual event sync triggered');
        return this.triggerSync();
    }

    async syncEventTalents() {
        this.logger.log('Event talents sync triggered');
        
        // Create a new sync log entry with 'running' status
        const syncLog = this.eventSyncLogRepository.create({
            startTime: new Date(),
            status: 'running',
            eventsFound: 0,
            eventsImported: 0,
            eventsSkipped: 0
        });
        
        await this.eventSyncLogRepository.save(syncLog);
        
        try {
            // Check if we should actually run the sync or just simulate it
            const shouldRunSync = process.env.ENABLE_EVENT_SYNC !== 'false';
            
            if (!shouldRunSync) {
                this.logger.log('Event talents sync disabled by environment variable. Recording sync log but not running actual sync.');
                
                // Create a simulated result
                const simulatedResult = {
                    talentsFound: 0,
                    talentsImported: 0,
                    talentsSkipped: 0,
                    newTalents: [],
                    skippedTalents: [],
                    imagesUploaded: [],
                    imagesFailed: []
                };
                
                // Update the sync log with the simulated results
                syncLog.endTime = new Date();
                syncLog.status = 'completed';
                syncLog.eventsFound = 0;
                syncLog.eventsImported = 0;
                syncLog.eventsSkipped = 0;
                syncLog.lastSyncDate = new Date();
                syncLog.details = {
                    newEvents: [],
                    skippedEvents: [],
                    imagesUploaded: [],
                    imagesFailed: [],
                    updatedEvents: [],
                    connectionStatus: { postgresql: true, azure: true },
                    syncDisabled: true
                };
                
                await this.eventSyncLogRepository.save(syncLog);
                return syncLog;
            }
            
            // Import the talent sync function
            const { migrateNewAzureTalents } = require('../../database/migrate-new-azure-talents');
            
            // Run the talent migration with the sync event talents flag
            const result = await migrateNewAzureTalents('talent', false, true);
            
            // Update the sync log with the results
            syncLog.endTime = new Date();
            syncLog.status = 'completed';
            syncLog.eventsFound = result.talentsFound || 0;
            syncLog.eventsImported = result.talentsImported || 0;
            syncLog.eventsSkipped = result.talentsSkipped || 0;
            syncLog.lastSyncDate = new Date();
            
            // Create details object
            const details: EventSyncDetails = {
                newEvents: result.newTalents || [],
                skippedEvents: result.skippedTalents?.map(t => ({ name: t.name, reason: t.reason })) || [],
                imagesUploaded: result.imagesUploaded || [],
                imagesFailed: result.imagesFailed?.map(i => ({ name: i.name, reason: i.reason })) || [],
                updatedEvents: [],
                connectionStatus: { postgresql: true, azure: true }
            };
            
            syncLog.details = details;
            
            await this.eventSyncLogRepository.save(syncLog);
            return syncLog;
        } catch (error) {
            // Update the sync log with the error
            syncLog.endTime = new Date();
            syncLog.status = 'failed';
            syncLog.error = error.message || 'Unknown error occurred during event talents sync';
            
            await this.eventSyncLogRepository.save(syncLog);
            throw error;
        }
    }

    private async triggerSync() {
        // Create a new sync log entry with 'running' status
        const syncLog = this.eventSyncLogRepository.create({
            startTime: new Date(),
            status: 'running',
            eventsFound: 0,
            eventsImported: 0,
            eventsSkipped: 0
        });
        
        await this.eventSyncLogRepository.save(syncLog);
        
        try {
            // Check if we should actually run the sync or just simulate it
            const shouldRunSync = process.env.ENABLE_EVENT_SYNC !== 'false';
            
            if (!shouldRunSync) {
                this.logger.log('Event sync disabled by environment variable. Recording sync log but not running actual sync.');
                
                // Create a simulated result
                const simulatedResult: EventSyncResult = {
                    eventsFound: 0,
                    eventsImported: 0,
                    eventsSkipped: 0,
                    lastSyncDate: new Date(),
                    newEvents: [],
                    skippedEvents: [],
                    imagesUploaded: [],
                    imagesFailed: [],
                    updatedEvents: [],
                    unaccountedEvents: [],
                    connectionStatus: { postgresql: true, azure: true }
                };
                
                // Update the sync log with the simulated results
                syncLog.endTime = new Date();
                syncLog.status = 'completed';
                syncLog.lastSyncDate = simulatedResult.lastSyncDate;
                syncLog.details = {
                    ...simulatedResult,
                    syncDisabled: true
                };
                
                await this.eventSyncLogRepository.save(syncLog);
                return syncLog;
            }
            
            // Setup Azure SQL connection
            const azureConfig = {
                server: process.env.AZURE_DB_SERVER || 'mssql.drv1.umbhost.net',
                database: process.env.AZURE_DB_NAME || 'TLM',
                user: process.env.AZURE_DB_USER || 'Reader',
                password: process.env.AZURE_DB_PASSWORD || 'TLM1234!',
                options: {
                    encrypt: false
                }
            };
            
            const azureConnection = await sql.connect(azureConfig);
            
            try {
                // Add a timeout to prevent long-running sync operations
                const syncPromise = this.migrationOrchestrator.migrateNewAzureEvents(azureConnection, false);
                
                // Create a timeout promise
                const timeoutPromise = new Promise<MigrationSummary>((_, reject) => {
                    setTimeout(() => {
                        reject(new Error('Event sync operation timed out after 25 minutes'));
                    }, 25 * 60 * 1000); // 25 minutes timeout
                });
                
                // Race the sync operation against the timeout
                const migrationResult = await Promise.race([syncPromise, timeoutPromise]);
                
                // Convert MigrationSummary to EventSyncResult format
                const result: EventSyncResult = {
                    eventsFound: migrationResult.totalProcessed,
                    eventsImported: migrationResult.created + migrationResult.updated,
                    eventsSkipped: migrationResult.skipped,
                    lastSyncDate: new Date(),
                    newEvents: [],
                    skippedEvents: [],
                    imagesUploaded: [],
                    imagesFailed: [],
                    updatedEvents: [],
                    unaccountedEvents: [],
                    connectionStatus: { postgresql: true, azure: true }
                };
                
                // Update the sync log with the results
                syncLog.endTime = new Date();
                syncLog.status = 'completed';
                syncLog.eventsFound = result.eventsFound || 0;
                syncLog.eventsImported = result.eventsImported || 0;
                syncLog.eventsSkipped = result.eventsSkipped || 0;
                syncLog.lastSyncDate = result.lastSyncDate || null;
                
                // Create details object
                const details: EventSyncDetails = {
                    newEvents: result.newEvents || [],
                    skippedEvents: result.skippedEvents || [],
                    imagesUploaded: result.imagesUploaded || [],
                    imagesFailed: result.imagesFailed || [],
                    updatedEvents: result.updatedEvents || [],
                    connectionStatus: result.connectionStatus || { postgresql: true, azure: true }
                };
                
                // Add unaccounted events if there are any
                if (result.eventsFound > (result.eventsImported + result.eventsSkipped)) {
                    details.unaccountedEvents = result.unaccountedEvents || [];
                }
                
                syncLog.details = details;
                
                await this.eventSyncLogRepository.save(syncLog);
                
                // Automatically run the date/time fix after migration
                try {
                    this.logger.log('Running automatic date/time fix after sync...');
                    const fixResult = await this.fixEventDatetimes();
                    this.logger.log(`Date/time fix completed successfully. Fixed ${fixResult.eventsImported} of ${fixResult.eventsFound} events.`);
                    
                    // Add note about the automatic fix to the original sync log
                    syncLog.notes = `Automatic date/time fix ran after sync and fixed ${fixResult.eventsImported} events.`;
                    await this.eventSyncLogRepository.save(syncLog);
                } catch (fixError) {
                    this.logger.error(`Error during automatic date/time fix: ${fixError.message}`, fixError.stack);
                    // Don't throw the error to avoid failing the entire sync
                    
                    // Add note about the failed fix attempt to the original sync log
                    syncLog.notes = `Automatic date/time fix attempted but failed: ${fixError.message}`;
                    await this.eventSyncLogRepository.save(syncLog);
                }
                
                return syncLog;
            } catch (syncError) {
                // Handle sync-specific errors
                this.logger.error(`Event sync failed: ${syncError.message}`, syncError.stack);
                throw syncError;
            } finally {
                // Always close the Azure connection
                try {
                    await azureConnection.close();
                } catch (closeError) {
                    this.logger.error(`Error closing Azure connection: ${closeError.message}`);
                }
            }
        } catch (error) {
            // Update the sync log with the error
            syncLog.endTime = new Date();
            syncLog.status = 'failed';
            syncLog.error = error.message || 'Unknown error occurred during event sync';
            
            this.logger.error(`Event sync failed: ${error.message}`, error.stack);
            await this.eventSyncLogRepository.save(syncLog);
            throw error;
        }
    }

    async fixEventDatetimes() {
        this.logger.log('Event datetime fix triggered');
        
        // Create a new sync log entry with 'running' status
        const syncLog = this.eventSyncLogRepository.create({
            startTime: new Date(),
            status: 'running',
            eventsFound: 0,
            eventsImported: 0,
            eventsSkipped: 0
        });
        
        await this.eventSyncLogRepository.save(syncLog);
        
        try {
            // Import the fix-event-datetimes function
            const { fixEventDatetimes } = require('../../database/fix-event-datetimes');
            
            // Run the datetime fix
            const result = await fixEventDatetimes(false); // Pass false for dryRun to actually apply changes
            
            // Update the sync log with the results
            syncLog.endTime = new Date();
            syncLog.status = 'completed';
            syncLog.eventsFound = result.eventsChecked || 0;
            syncLog.eventsImported = result.eventsFixed || 0;
            syncLog.eventsSkipped = result.eventsSkipped || 0;
            syncLog.lastSyncDate = new Date();
            
            // Create details object with notes about the datetime fix
            const details: EventSyncDetails = {
                newEvents: [],
                skippedEvents: [],
                imagesUploaded: [],
                imagesFailed: [],
                updatedEvents: result.fixedEvents || [],
                connectionStatus: { postgresql: true, azure: true },
                syncDisabled: false
            };
            
            // Store notes in the error field since we don't have a dedicated notes field
            syncLog.error = `Fixed ${result.eventsFixed || 0} event datetime issues out of ${result.eventsChecked || 0} events checked.`;
            
            syncLog.details = details;
            
            await this.eventSyncLogRepository.save(syncLog);
            return syncLog;
        } catch (error) {
            // Update the sync log with the error
            syncLog.endTime = new Date();
            syncLog.status = 'failed';
            syncLog.error = error.message || 'Unknown error occurred during event datetime fix';
            
            await this.eventSyncLogRepository.save(syncLog);
            throw error;
        }
    }

    async fixEventImages(): Promise<EventSyncLog> {
        this.logger.log('Starting event image fix process');

        // Create a new sync log entry
        const syncLog = new EventSyncLog();
        syncLog.startTime = new Date();
        syncLog.status = 'running';
        syncLog.syncType = 'image_fix';
        syncLog.details = {
            newEvents: [],
            skippedEvents: [],
            imagesUploaded: [],
            imagesFailed: [],
            updatedEvents: [],
            connectionStatus: { postgresql: true, azure: true },
            syncDisabled: false
        };

        await this.eventSyncLogRepository.save(syncLog);

        try {
            // Import the fix-event-images function
            const { processEventImagesFromAzure } = require('../../scripts/fix-event-images');

            // Run the image fix using the existing DataSource
            const result = await processEventImagesFromAzure(this.dataSource, false);

            // Update the sync log with the results
            syncLog.endTime = new Date();
            syncLog.status = 'completed';
            syncLog.eventsFound = result.total || 0;
            syncLog.eventsImported = result.success || 0;
            syncLog.eventsSkipped = result.skipped || 0;
            syncLog.lastSyncDate = new Date();

            // Update details with the fixed events
            const details: EventSyncDetails = {
                newEvents: [],
                skippedEvents: [],
                imagesUploaded: result.fixedEvents || [],
                imagesFailed: [],
                updatedEvents: result.fixedEvents || [],
                connectionStatus: { postgresql: true, azure: true },
                syncDisabled: false
            };

            syncLog.details = details;
            syncLog.error = null;

            this.logger.log(`Event image fix completed: ${result.success} images processed successfully`);

            return syncLog;

        } catch (error) {
            this.logger.error('Error during event image fix:', error);

            syncLog.endTime = new Date();
            syncLog.status = 'failed';
            syncLog.error = error instanceof Error ? error.message : 'Unknown error during image fix';

            await this.eventSyncLogRepository.save(syncLog);
            throw error;
        } finally {
            await this.eventSyncLogRepository.save(syncLog);
        }
    }
}
