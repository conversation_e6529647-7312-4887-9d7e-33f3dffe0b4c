"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/admin/event-relations/talent.tsx":
/*!*****************************************************!*\
  !*** ./components/admin/event-relations/talent.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventTalentRelations: () => (/* binding */ EventTalentRelations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-auth */ \"(app-pages-browser)/./hooks/use-auth.ts\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ EventTalentRelations auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction EventTalentRelations() {\n    _s();\n    const [counts, setCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [missingEvents, setMissingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mismatchedEvents, setMismatchedEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isBulkFixing, setIsBulkFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [batchFixing, setBatchFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [csvFixing, setCsvFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Add this line\n    const [batchFixProgress, setBatchFixProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 0,\n        total: 0,\n        success: 0,\n        failed: 0\n    });\n    const [fixingEvents, setFixingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // Track which events are being fixed\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        missing: false,\n        mismatched: false\n    });\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { getAccessToken } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // Handle CSV-based fix for all event-talent relationships\n    const handleCsvFix = async ()=>{\n        try {\n            setCsvFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            console.log('🔧 [Frontend] Starting CSV-based fix for event-talent relationships');\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/fix-from-csv\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('🔧 [Frontend] Server error response:', errorText);\n                throw new Error(\"Failed to execute CSV fix: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('🔧 [Frontend] CSV fix completed successfully:', result);\n            toast({\n                title: 'CSV Fix Complete',\n                description: result.message || 'Successfully processed event-talent relationships from CSV.',\n                variant: 'default'\n            });\n            // Refresh data to show updated counts\n            fetchData();\n        } catch (err) {\n            console.error('🔧 [Frontend] Error during CSV fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete CSV fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setCsvFixing(false);\n        }\n    };\n    // Fetch data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventTalentRelations.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"EventTalentRelations.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            setIsLoading(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Only fetch basic counts - avoid expensive missing/mismatched queries\n            const countsResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations?year=2025\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!countsResponse.ok) {\n                throw new Error('Failed to fetch event-talent relationship counts');\n            }\n            const countsData = await countsResponse.json();\n            setCounts(countsData);\n            // Skip the expensive missing/mismatched queries on initial load\n            // These will only be loaded when the user expands the sections\n            setMissingEvents([]);\n            setMismatchedEvents([]);\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching event-talent relationship data', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch event-talent relationship data');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Load expensive data only when sections are expanded\n    const loadSectionData = async (section)=>{\n        try {\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            if (section === 'missing' && missingEvents.length === 0) {\n                console.log('Loading missing events data...');\n                const missingResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/missing?year=2025\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token)\n                    }\n                });\n                if (missingResponse.ok) {\n                    const missingData = await missingResponse.json();\n                    setMissingEvents(missingData);\n                }\n            }\n            if (section === 'mismatched' && mismatchedEvents.length === 0) {\n                console.log('Loading mismatched events data...');\n                const mismatchedResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/mismatched?year=2025\"), {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token)\n                    }\n                });\n                if (mismatchedResponse.ok) {\n                    const mismatchedData = await mismatchedResponse.json();\n                    setMismatchedEvents(mismatchedData);\n                }\n            }\n        } catch (err) {\n            console.error(\"Error loading \".concat(section, \" events data\"), err);\n            toast({\n                title: 'Error',\n                description: \"Failed to load \".concat(section, \" events data\"),\n                variant: 'destructive'\n            });\n        }\n    };\n    const toggleExpanded = async (section)=>{\n        const isCurrentlyExpanded = expanded[section];\n        // If expanding and no data loaded yet, load it first\n        if (!isCurrentlyExpanded) {\n            await loadSectionData(section);\n        }\n        setExpanded((prev)=>({\n                ...prev,\n                [section]: !prev[section]\n            }));\n    };\n    const handleFixAll = async ()=>{\n        try {\n            setIsBulkFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Pass year=2025 to match the same filter used in fetchData() for event display\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/fix-all-aggregated?year=2025\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fix all event-talent relationships');\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: result.message,\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing all event-talent relationships', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to fix all event-talent relationships',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsBulkFixing(false);\n        }\n    };\n    // Handle batch fixing using the bulk fix endpoint for better performance\n    const handleBatchFix = async ()=>{\n        try {\n            setBatchFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Check if there are events that need fixing\n            if (missingEvents.length === 0 && mismatchedEvents.length === 0) {\n                toast({\n                    title: 'No Events to Fix',\n                    description: 'There are no events that need fixing.',\n                    variant: 'default'\n                });\n                return;\n            }\n            console.log(\"\\uD83D\\uDD27 [Frontend] Starting bulk batch fix for all events\");\n            // Use the new aggregated batch fix endpoint which calls individual fixes for each event\n            // IMPORTANT: Pass the same year filter (2025) that's used for displaying events in fetchData()\n            // This ensures the batch fix processes the exact same events shown in the UI\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/fix-all-aggregated?year=2025\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to execute batch fix: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDD27 [Frontend] Batch fix completed successfully\");\n            toast({\n                title: 'Batch Fix Complete',\n                description: result.message || 'Successfully completed batch fix operation.',\n                variant: 'default'\n            });\n            // Refresh data to show updated counts\n            fetchData();\n        } catch (err) {\n            console.error('Error during batch fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete batch fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setBatchFixing(false);\n        }\n    };\n    // Open confirmation dialog for fixing an event\n    const openFixConfirmDialog = (event)=>{\n        setSelectedEvent(event);\n        setConfirmDialogOpen(true);\n    };\n    // Handle fixing a single event's talent relationships\n    const handleFixSingleEvent = async ()=>{\n        if (!selectedEvent) return;\n        try {\n            // Close the dialog\n            setConfirmDialogOpen(false);\n            // Mark this event as being fixed\n            setFixingEvents((prev)=>({\n                    ...prev,\n                    [selectedEvent.id]: true\n                }));\n            console.log(\"\\uD83D\\uDD27 [Frontend] Starting fix for event: \".concat(selectedEvent.name, \" (ID: \").concat(selectedEvent.id, \")\"));\n            console.log(\"\\uD83D\\uDD27 [Frontend] Event details:\", {\n                id: selectedEvent.id,\n                name: selectedEvent.name,\n                azure_talent_ids: selectedEvent.azure_talent_ids,\n                postgres_talent_count: selectedEvent.postgres_talent_count\n            });\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const url = \"\".concat(baseUrl, \"/admin/event-talent-relations/fix/\").concat(selectedEvent.id);\n            console.log(\"\\uD83D\\uDD27 [Frontend] Making POST request to: \".concat(url));\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            console.log(\"\\uD83D\\uDD27 [Frontend] Response status: \".concat(response.status, \" \").concat(response.statusText));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"\\uD83D\\uDD27 [Frontend] Server error response:\", errorText);\n                throw new Error(\"Failed to fix event-talent relationship for \".concat(selectedEvent.name, \": \").concat(response.status, \" \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDD27 [Frontend] Success response:\", result);\n            toast({\n                title: 'Success',\n                description: result.message || 'Fixed musician relationships for \"'.concat(selectedEvent.name, '\"'),\n                variant: 'default'\n            });\n            console.log(\"\\uD83D\\uDD27 [Frontend] Refreshing data after successful fix\");\n            // Refresh data after successful fix\n            fetchData();\n        } catch (err) {\n            console.error('🔧 [Frontend] Error fixing event-talent relationship', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : \"Failed to fix event-talent relationship for \".concat(selectedEvent.name),\n                variant: 'destructive'\n            });\n        } finally{\n            // Clear the fixing state for this event\n            if (selectedEvent) {\n                setFixingEvents((prev)=>({\n                        ...prev,\n                        [selectedEvent.id]: false\n                    }));\n            }\n            // Clear the selected event\n            setSelectedEvent(null);\n            console.log(\"\\uD83D\\uDD27 [Frontend] Fix operation completed\");\n        }\n    };\n    // Determine status message and icon color\n    const getStatusDetails = ()=>{\n        if (!counts) return {\n            message: 'Loading...',\n            status: 'neutral'\n        };\n        // Check if there are any actual problematic relationships in the dropdown lists\n        const hasProblematicRelations = missingEvents.length > 0 || mismatchedEvents.length > 0;\n        // If there are no problematic relations to troubleshoot, show green\n        // Only show yellow warning when there's something that needs attention\n        if (!hasProblematicRelations) {\n            return {\n                message: counts.missingTalents === 0 ? 'All events have talent relationships' : \"\".concat(counts.missingTalents, \" events with no musician relations\"),\n                status: 'good'\n            };\n        }\n        // Show yellow warning when there are items in the dropdown lists that need attention\n        return {\n            message: \"\".concat(counts.missingTalents, \" events (2025+) with no musician relations\"),\n            status: 'warning'\n        };\n    };\n    // Get the appropriate icon color based on status\n    const getIconColorClass = (status)=>{\n        switch(status){\n            case 'good':\n                return 'text-green-500';\n            case 'warning':\n                return 'text-yellow-500';\n            case 'bad':\n                return 'text-red-500';\n            default:\n                return 'text-muted-foreground';\n        }\n    };\n    const statusDetails = getStatusDetails();\n    // Format date for display\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'No date';\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"flex flex-row items-center space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-5 w-5 mr-2 \".concat(getIconColorClass(statusDetails.status))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-xl\",\n                            children: \"Events - Musicians\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                lineNumber: 453,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center justify-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-8 w-8 animate-spin text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-muted-foreground\",\n                            children: \"Loading relationship data...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 11\n                }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-8 px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-5 w-5 text-red-500 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-500\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: statusDetails.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleBatchFix,\n                                        disabled: batchFixing || missingEvents.length === 0,\n                                        className: \"flex items-center\",\n                                        variant: \"outline\",\n                                        children: batchFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Fixing \",\n                                                batchFixProgress.current,\n                                                \"/\",\n                                                batchFixProgress.total,\n                                                \"...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Batch Fix (10 Events)\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleCsvFix,\n                                        disabled: batchFixing || csvFixing,\n                                        className: \"flex items-center\",\n                                        variant: \"outline\",\n                                        children: csvFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Validating CSV...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Validate from CSV\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: fetchData,\n                                        disabled: isLoading || batchFixing || csvFixing,\n                                        title: \"Refresh data\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 15\n                            }, this),\n                            batchFixing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-2 bg-muted rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm mb-1 flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Batch Fix Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    batchFixProgress.current,\n                                                    \"/\",\n                                                    batchFixProgress.total,\n                                                    \" events\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-secondary h-2 rounded-full overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary h-full transition-all duration-300 ease-in-out\",\n                                            style: {\n                                                width: \"\".concat(batchFixProgress.current / Math.max(batchFixProgress.total, 1) * 100, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-xs mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-500\",\n                                                children: [\n                                                    batchFixProgress.success,\n                                                    \" successful\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: [\n                                                    batchFixProgress.failed,\n                                                    \" failed\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                        open: expanded.missing,\n                                        onOpenChange: ()=>toggleExpanded('missing'),\n                                        className: \"border rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Events Missing Musician Relationships (\",\n                                                                    missingEvents.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    expanded.missing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                className: \"px-4 pb-4\",\n                                                children: missingEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground py-2\",\n                                                    children: \"No events missing musician relationships\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Event Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 604,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Venue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Azure Talent IDs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"PostgreSQL Talents\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Action\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 608,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                            children: missingEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            className: \"font-medium\",\n                                                                            children: event.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 614,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: formatDate(event.date)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 615,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: event.venue_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 616,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: event.azure_talent_ids.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 617,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            className: \"text-red-500\",\n                                                                            children: event.postgres_talent_count\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 618,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>openFixConfirmDialog(event),\n                                                                                disabled: fixingEvents[event.id],\n                                                                                children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                            className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                                            lineNumber: 628,\n                                                                                            columnNumber: 39\n                                                                                        }, this),\n                                                                                        \"Fixing...\"\n                                                                                    ]\n                                                                                }, void 0, true) : \"Fix\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                                lineNumber: 620,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 619,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, event.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                        open: expanded.mismatched,\n                                        onOpenChange: ()=>toggleExpanded('mismatched'),\n                                        className: \"border rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Events with Mismatched Musician Counts (\",\n                                                                    mismatchedEvents.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    expanded.mismatched ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                className: \"px-4 pb-4\",\n                                                children: mismatchedEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground py-2\",\n                                                    children: \"No events with mismatched musician counts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Event Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 669,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 670,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Venue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 671,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Azure Talent IDs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 672,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"PostgreSQL Talents\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 673,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Action\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 668,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                            children: mismatchedEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            className: \"font-medium\",\n                                                                            children: event.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: formatDate(event.date)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 681,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: event.venue_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: event.azure_talent_ids.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 683,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            className: \"text-yellow-500\",\n                                                                            children: event.postgres_talent_count\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>openFixConfirmDialog(event),\n                                                                                disabled: fixingEvents[event.id],\n                                                                                children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                            className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                                            lineNumber: 694,\n                                                                                            columnNumber: 39\n                                                                                        }, this),\n                                                                                        \"Fixing...\"\n                                                                                    ]\n                                                                                }, void 0, true) : \"Fix\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                                lineNumber: 686,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, event.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                lineNumber: 460,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: confirmDialogOpen,\n                onOpenChange: setConfirmDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                    children: \"Fix Event-Musician Relationship\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                                    children: selectedEvent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 py-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Are you sure you want to fix the musician relationships for this event?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted p-3 rounded-md text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Event:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            selectedEvent.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Date:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            formatDate(selectedEvent.date)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Venue:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 728,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            selectedEvent.venue_name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Azure Talents:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            selectedEvent.azure_talent_ids.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"PostgreSQL Talents:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            selectedEvent.postgres_talent_count\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 725,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"This will:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-5 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Retrieve talent relationships from Azure SQL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Update the PostgreSQL database to match\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Add missing talent relationships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Remove incorrect talent relationships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 718,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setConfirmDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: handleFixSingleEvent,\n                                    children: \"Fix\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                    lineNumber: 717,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                lineNumber: 716,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n        lineNumber: 452,\n        columnNumber: 5\n    }, this);\n}\n_s(EventTalentRelations, \"s5EYwvXrK6FOy5+KAwWcUYbv0pk=\", false, function() {\n    return [\n        _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = EventTalentRelations;\nvar _c;\n$RefreshReg$(_c, \"EventTalentRelations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/event-relations/talent.tsx\n"));

/***/ })

});