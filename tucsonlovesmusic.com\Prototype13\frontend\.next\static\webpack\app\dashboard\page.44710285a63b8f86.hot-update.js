"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/admin/id-validation/index.tsx":
/*!**************************************************!*\
  !*** ./components/admin/id-validation/index.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IdValidation: () => (/* binding */ IdValidation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,Clock,Copy,Database,Image,Loader2,MapPin,MergeIcon,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-auth */ \"(app-pages-browser)/./hooks/use-auth.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./lib/config.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _merge_dialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./merge-dialog */ \"(app-pages-browser)/./components/admin/id-validation/merge-dialog.tsx\");\n/* harmony import */ var _fix_dialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./fix-dialog */ \"(app-pages-browser)/./components/admin/id-validation/fix-dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ IdValidation auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction IdValidation() {\n    var _issuesByType_events, _selectedIssue_postgresRecord, _selectedIssue_azureRecord, _selectedIssue_postgresRecord1, _selectedIssue_azureRecord1, _selectedIssue_postgresRecord2, _selectedIssue_azureRecord2;\n    _s();\n    // State for validation data\n    const [validationCounts, setValidationCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [issuesByType, setIssuesByType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loadingCounts, setLoadingCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingIssues, setLoadingIssues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeEntityType, setActiveEntityType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Dialogs state\n    const [fixDialogOpen, setFixDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mergeDialogOpen, setMergeDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIssue, setSelectedIssue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isMerging, setIsMerging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMatchId, setSelectedMatchId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Expanded state for collapsible sections\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        venues: false,\n        musicians: false,\n        events: false\n    });\n    const [syncResults, setSyncResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageFixResults, setImageFixResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSyncing, setIsSyncing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFixingImages, setIsFixingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchFixing, setIsBatchFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [batchFixResults, setBatchFixResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { getAccessToken } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    // Toggle expanded state for a specific section\n    const toggleExpanded = (section)=>{\n        setExpanded((prev)=>({\n                ...prev,\n                [section]: !prev[section]\n            }));\n        // If we're expanding a section and don't have the data yet, fetch it\n        if (!expanded[section] && section === 'venues' && (!issuesByType.venues || issuesByType.venues.length === 0)) {\n            fetchIssues('venues');\n        } else if (!expanded[section] && section === 'musicians' && (!issuesByType.musicians || issuesByType.musicians.length === 0)) {\n            fetchIssues('musicians');\n        } else if (!expanded[section] && section === 'events' && (!issuesByType.events || issuesByType.events.length === 0)) {\n            fetchIssues('events');\n        }\n    };\n    // Fetch validation counts on load\n    // Complete refresh function - exactly matching duplicate detection approach\n    const refreshData = async ()=>{\n        try {\n            setLoadingCounts(true);\n            setError(null);\n            const apiUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const token = await getAccessToken();\n            // Fetch fresh validation counts\n            const countsResponse = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/counts\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (!countsResponse.ok) {\n                throw new Error(\"Failed to fetch validation counts\");\n            }\n            const data = await countsResponse.json();\n            console.log('Fetched validation counts:', data);\n            // Update counts\n            setValidationCounts(data);\n            // CRITICAL: Complete reset of all issues data\n            // This is the key difference from our previous approach\n            setIssuesByType({});\n            // Fetch venue issues if count > 0 and section is expanded\n            if (data.venueCount > 0 && (expanded.venues || activeEntityType === 'venues')) {\n                console.log('Fetching venue validation issues');\n                const timestamp = new Date().getTime(); // Add cache busting\n                const venueResponse = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/issues/venues?t=\").concat(timestamp), {\n                    method: \"GET\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(token),\n                        \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                        \"Pragma\": \"no-cache\"\n                    }\n                });\n                if (venueResponse.ok) {\n                    const venueData = await venueResponse.json();\n                    console.log(\"Received \".concat(venueData.length, \" venue issues\"));\n                    setIssuesByType((prev)=>({\n                            ...prev,\n                            venues: venueData\n                        }));\n                }\n            }\n            // Fetch musician issues if count > 0 and section is expanded\n            if (data.musicianCount > 0 && (expanded.musicians || activeEntityType === 'musicians')) {\n                console.log('Fetching musician validation issues');\n                const timestamp = new Date().getTime(); // Add cache busting\n                const musicianResponse = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/issues/musicians?t=\").concat(timestamp), {\n                    method: \"GET\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(token),\n                        \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                        \"Pragma\": \"no-cache\"\n                    }\n                });\n                if (musicianResponse.ok) {\n                    const musicianData = await musicianResponse.json();\n                    console.log(\"Received \".concat(musicianData.length, \" musician issues\"));\n                    setIssuesByType((prev)=>({\n                            ...prev,\n                            musicians: musicianData\n                        }));\n                }\n            }\n            // Fetch event issues if count > 0 and section is expanded\n            if (data.eventCount > 0 && (expanded.events || activeEntityType === 'events')) {\n                console.log('Fetching event validation issues');\n                const timestamp = new Date().getTime(); // Add cache busting\n                const eventResponse = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/issues/events?t=\").concat(timestamp), {\n                    method: \"GET\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(token),\n                        \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                        \"Pragma\": \"no-cache\"\n                    }\n                });\n                if (eventResponse.ok) {\n                    const eventData = await eventResponse.json();\n                    console.log(\"Received \".concat(eventData.length, \" event issues\"));\n                    setIssuesByType((prev)=>({\n                            ...prev,\n                            events: eventData\n                        }));\n                }\n            }\n            return data;\n        } catch (err) {\n            console.error(\"Error refreshing data:\", err);\n            setError(\"Failed to refresh validation data. Please try again.\");\n            toast({\n                title: \"Error\",\n                description: \"Failed to refresh validation data. Please try again.\",\n                variant: \"destructive\"\n            });\n            return null;\n        } finally{\n            setLoadingCounts(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IdValidation.useEffect\": ()=>{\n            refreshData();\n        }\n    }[\"IdValidation.useEffect\"], []);\n    // Fetch issues when an entity type is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IdValidation.useEffect\": ()=>{\n            if (activeEntityType) {\n                fetchIssues(activeEntityType);\n            }\n        }\n    }[\"IdValidation.useEffect\"], [\n        activeEntityType\n    ]);\n    // Fetch validation counts\n    const fetchValidationCounts = async ()=>{\n        try {\n            setLoadingCounts(true);\n            setError(null);\n            const apiUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const token = await getAccessToken();\n            const response = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/counts\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch validation counts\");\n            }\n            const data = await response.json();\n            setValidationCounts(data);\n            // Return the data for direct use\n            return data;\n        } catch (err) {\n            console.error(\"Error fetching validation counts:\", err);\n            setError(\"Failed to fetch validation counts. Please try again.\");\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch validation counts. Please try again.\",\n                variant: \"destructive\"\n            });\n            // Return null in case of error\n            return null;\n        } finally{\n            setLoadingCounts(false);\n        }\n    };\n    // Fetch issues for a specific entity type\n    const fetchIssues = async (entityType)=>{\n        try {\n            setLoadingIssues(true);\n            setError(null);\n            const apiUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const token = await getAccessToken();\n            console.log(\"Fetching \".concat(entityType, \" issues\"));\n            const response = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/issues/\").concat(entityType), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch \".concat(entityType, \" issues\"));\n            }\n            const data = await response.json();\n            console.log(\"Received \".concat(data.length, \" \").concat(entityType, \" issues\"));\n            // Debug: Log the breakdown of issue types\n            const missingAzure = data.filter((issue)=>issue.issueType === 'missing_azure').length;\n            const missingPostgres = data.filter((issue)=>issue.issueType === 'missing_postgres').length;\n            console.log(\"Issue breakdown - missing_azure: \".concat(missingAzure, \", missing_postgres: \").concat(missingPostgres));\n            // Debug: Log a sample of each issue type if available\n            if (missingAzure > 0) {\n                console.log('Sample missing_azure issue:', data.find((issue)=>issue.issueType === 'missing_azure'));\n            }\n            if (missingPostgres > 0) {\n                console.log('Sample missing_postgres issue:', data.find((issue)=>issue.issueType === 'missing_postgres'));\n            }\n            // Apply a complete replacement of data rather than merging\n            setIssuesByType((prev)=>({\n                    ...prev,\n                    [entityType]: data\n                }));\n            return data;\n        } catch (err) {\n            console.error(\"Error fetching \".concat(entityType, \" issues:\"), err);\n            setError(\"Failed to fetch \".concat(entityType, \" issues. Please try again.\"));\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch \".concat(entityType, \" issues. Please try again.\"),\n                variant: \"destructive\"\n            });\n            return [];\n        } finally{\n            setLoadingIssues(false);\n        }\n    };\n    // Handle opening fix dialog for an issue\n    const handleFixIssue = (issue)=>{\n        setSelectedIssue(issue);\n        setFixDialogOpen(true);\n    };\n    // Handle opening merge dialog for an issue\n    const handleMergeIssue = (issue)=>{\n        setSelectedIssue(issue);\n        setMergeDialogOpen(true);\n    };\n    // Handle resolving an issue\n    const handleResolveIssue = async (issueId, resolution, options)=>{\n        try {\n            setIsMerging(true);\n            const apiUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const token = await getAccessToken();\n            const payload = {\n                resolution\n            };\n            // Handle different types of the options parameter\n            if (typeof options === 'string') {\n                // It's a regular matchId\n                payload.matchId = options;\n            } else if (options && typeof options === 'object') {\n                // It's an options object with possible azureId and mergeFields\n                if ('azureId' in options) {\n                    payload.azureId = options.azureId;\n                }\n                if ('mergeFields' in options) {\n                    payload.mergeFields = options.mergeFields;\n                }\n            }\n            // Close any open dialogs right away for better UX\n            setFixDialogOpen(false);\n            setMergeDialogOpen(false);\n            // Now send the API request\n            const response = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/resolve/\").concat(issueId), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify(payload)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to resolve issue\");\n            }\n            toast({\n                title: \"Success\",\n                description: \"Issue resolved successfully\"\n            });\n            // Simple, direct approach - exactly like duplicate detection component\n            const latestCounts = await refreshData();\n            // Auto-collapse sections when they reach zero count\n            if (latestCounts) {\n                if (activeEntityType === 'venues' && latestCounts.venueCount === 0) {\n                    setExpanded((prev)=>({\n                            ...prev,\n                            venues: false\n                        }));\n                } else if (activeEntityType === 'musicians' && latestCounts.musicianCount === 0) {\n                    setExpanded((prev)=>({\n                            ...prev,\n                            musicians: false\n                        }));\n                } else if (activeEntityType === 'events' && latestCounts.eventCount === 0) {\n                    setExpanded((prev)=>({\n                            ...prev,\n                            events: false\n                        }));\n                }\n            }\n        } catch (err) {\n            console.error(\"Error resolving issue:\", err);\n            toast({\n                title: \"Error\",\n                description: \"Failed to resolve issue. Please try again.\",\n                variant: \"destructive\"\n            });\n            // If there was an error, do a complete refresh to ensure accurate state\n            await refreshData();\n        } finally{\n            setIsMerging(false);\n        }\n    };\n    // Handle merge for duplicate records\n    const handleMerge = async (primaryId, duplicateIds)=>{\n        try {\n            setIsMerging(true);\n            const apiUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const token = await getAccessToken();\n            const response = await fetch(\"\".concat(apiUrl, \"/admin/id-validation/merge\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    primaryId,\n                    duplicateIds\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to merge records\");\n            }\n            toast({\n                title: \"Success\",\n                description: \"Records merged successfully\"\n            });\n            // Refresh the issues and counts\n            if (activeEntityType) {\n                fetchIssues(activeEntityType);\n            }\n            fetchValidationCounts();\n            // Close any open dialogs\n            setMergeDialogOpen(false);\n            return true;\n        } catch (error) {\n            console.error(\"Error merging records:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to merge records. Please try again.\",\n                variant: \"destructive\"\n            });\n            return false;\n        } finally{\n            setIsMerging(false);\n        }\n    };\n    // Open merge dialog when duplicate group is selected\n    const showMergeDialog = (ids, metadata)=>{\n        // Only open the merge dialog if there's a selected issue\n        if (selectedIssue) {\n            setMergeDialogOpen(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-5 w-5 \".concat(!validationCounts || parseInt(validationCounts.venueCount) === 0 && parseInt(validationCounts.musicianCount) === 0 && parseInt(validationCounts.eventCount) === 0 ? 'text-green-500' : 'text-yellow-500')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, this),\n                        \"ID Validation\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                    lineNumber: 497,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-destructive/10 border border-destructive rounded-md p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-5 w-5 text-destructive\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-medium text-destructive\",\n                                    children: [\n                                        error,\n                                        \". Please try again.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 11\n                    }, this),\n                    loadingCounts && !validationCounts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-8 w-8 animate-spin mx-auto mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading ID validation data...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.Collapsible, {\n                                            open: expanded.venues,\n                                            onOpenChange: ()=>toggleExpanded('venues'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Venue ID Validation Issues (\",\n                                                                        (validationCounts === null || validationCounts === void 0 ? void 0 : validationCounts.venueCount) || 0,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        expanded.venues ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: loadingIssues ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 animate-spin mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Loading venue issues...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 25\n                                                    }, this) : issuesByType.venues && issuesByType.venues.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 654,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"PostgreSQL ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 655,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Azure ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 656,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Issue Type\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 657,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 658,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                                                                children: issuesByType.venues.map((issue)=>{\n                                                                    var _issue_postgresRecord, _issue_azureRecord, _issue_postgresRecord1, _issue_azureRecord1;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: ((_issue_postgresRecord = issue.postgresRecord) === null || _issue_postgresRecord === void 0 ? void 0 : _issue_postgresRecord.name) || ((_issue_azureRecord = issue.azureRecord) === null || _issue_azureRecord === void 0 ? void 0 : _issue_azureRecord.name) || 'Unknown'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 664,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: ((_issue_postgresRecord1 = issue.postgresRecord) === null || _issue_postgresRecord1 === void 0 ? void 0 : _issue_postgresRecord1.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-mono text-xs\",\n                                                                                    children: [\n                                                                                        issue.postgresRecord.id.substring(0, 8),\n                                                                                        \"...\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 669,\n                                                                                    columnNumber: 37\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-muted-foreground text-xs italic\",\n                                                                                    children: \"Missing\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 671,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 667,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: ((_issue_azureRecord1 = issue.azureRecord) === null || _issue_azureRecord1 === void 0 ? void 0 : _issue_azureRecord1.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-mono text-xs\",\n                                                                                    children: [\n                                                                                        issue.azureRecord.id.substring(0, 8),\n                                                                                        \"...\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 676,\n                                                                                    columnNumber: 37\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-muted-foreground text-xs italic\",\n                                                                                    children: \"Missing\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 678,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 674,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                                    children: issue.issueType === 'missing_azure' ? 'missing azure' : issue.issueType.replace('_', ' ')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 682,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 681,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex space-x-2\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleFixIssue(issue),\n                                                                                        children: \"Fix\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 688,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 687,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 686,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, issue.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                        lineNumber: 663,\n                                                                        columnNumber: 31\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 661,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No venue ID validation issues found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                        lineNumber: 702,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.Collapsible, {\n                                            open: expanded.musicians,\n                                            onOpenChange: ()=>toggleExpanded('musicians'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 715,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Musician ID Validation Issues (\",\n                                                                        (validationCounts === null || validationCounts === void 0 ? void 0 : validationCounts.musicianCount) || 0,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 716,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        expanded.musicians ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: loadingIssues ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 animate-spin mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 728,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Loading musician issues...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 25\n                                                    }, this) : issuesByType.musicians && issuesByType.musicians.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 736,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"PostgreSQL ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 737,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Issue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 738,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Created\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 739,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 740,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                                                                children: issuesByType.musicians.map((issue)=>{\n                                                                    var _issue_postgresRecord, _issue_postgresRecord1;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                className: \"font-medium\",\n                                                                                children: ((_issue_postgresRecord = issue.postgresRecord) === null || _issue_postgresRecord === void 0 ? void 0 : _issue_postgresRecord.name) || 'Unknown'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 746,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                        className: \"bg-secondary/50 px-1 py-0.5 rounded text-xs\",\n                                                                                        children: issue.id\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 748,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"icon\",\n                                                                                        className: \"h-6 w-6 ml-1\",\n                                                                                        onClick: ()=>{\n                                                                                            navigator.clipboard.writeText(issue.id);\n                                                                                            toast({\n                                                                                                title: \"Copied to clipboard\",\n                                                                                                description: \"The musician ID has been copied to your clipboard.\"\n                                                                                            });\n                                                                                        },\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"h-3 w-3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 763,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 751,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 747,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: issue.issueType\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 766,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: ((_issue_postgresRecord1 = issue.postgresRecord) === null || _issue_postgresRecord1 === void 0 ? void 0 : _issue_postgresRecord1.createdAt) ? new Date(issue.postgresRecord.createdAt).toLocaleDateString() : 'Unknown'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 767,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex space-x-2\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>handleFixIssue(issue),\n                                                                                        children: \"Fix\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 774,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 773,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 772,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, issue.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No musician ID validation issues found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.Collapsible, {\n                                            open: expanded.events,\n                                            onOpenChange: ()=>toggleExpanded('events'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 802,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Event ID Validation Issues (\",\n                                                                        (validationCounts === null || validationCounts === void 0 ? void 0 : validationCounts.eventCount) || 0,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        expanded.events ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-4 flex flex-col sm:flex-row gap-2 justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"flex items-center\",\n                                                                    disabled: isSyncing || isFixingImages,\n                                                                    onClick: async ()=>{\n                                                                        try {\n                                                                            setIsFixingImages(true);\n                                                                            const token = await getAccessToken();\n                                                                            const response = await fetch(\"\".concat((0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)(), \"/admin/events/sync/fix-event-images\"), {\n                                                                                method: 'POST',\n                                                                                headers: {\n                                                                                    'Content-Type': 'application/json',\n                                                                                    'Authorization': \"Bearer \".concat(token)\n                                                                                }\n                                                                            });\n                                                                            if (!response.ok) {\n                                                                                throw new Error('Failed to fix event images');\n                                                                            }\n                                                                            const result = await response.json();\n                                                                            // Check if this is the new message response\n                                                                            if (result.message) {\n                                                                                toast({\n                                                                                    title: \"Manual Action Required\",\n                                                                                    description: result.message,\n                                                                                    variant: \"default\"\n                                                                                });\n                                                                                return;\n                                                                            }\n                                                                            // Store the image fix results for display\n                                                                            setImageFixResults({\n                                                                                total: result.total || 0,\n                                                                                processed: result.processed || 0,\n                                                                                success: result.success || 0,\n                                                                                skipped: result.skipped || 0,\n                                                                                errors: result.errors || 0,\n                                                                                timestamp: new Date().toLocaleString()\n                                                                            });\n                                                                            toast({\n                                                                                title: \"Image Fix Complete\",\n                                                                                description: \"Fixed \".concat(result.success, \" event images out of \").concat(result.total, \" events checked.\")\n                                                                            });\n                                                                            // Refresh the validation issues after fixing images\n                                                                            refreshData();\n                                                                        } catch (error) {\n                                                                            console.error('Error fixing event images:', error);\n                                                                            toast({\n                                                                                title: \"Image Fix Failed\",\n                                                                                description: \"Failed to fix event images. See console for details.\",\n                                                                                variant: \"destructive\"\n                                                                            });\n                                                                        } finally{\n                                                                            setIsFixingImages(false);\n                                                                            setLoadingIssues(false);\n                                                                        }\n                                                                    },\n                                                                    children: isFixingImages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 880,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Processing...\"\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 885,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Fix Event Images\"\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 815,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"flex items-center\",\n                                                                    disabled: isSyncing || isFixingImages,\n                                                                    onClick: async ()=>{\n                                                                        try {\n                                                                            var _result_details;\n                                                                            setIsSyncing(true);\n                                                                            const token = await getAccessToken();\n                                                                            const response = await fetch(\"\".concat((0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)(), \"/admin/events/sync/fix-event-datetimes\"), {\n                                                                                method: 'POST',\n                                                                                headers: {\n                                                                                    'Authorization': \"Bearer \".concat(token),\n                                                                                    'Content-Type': 'application/json'\n                                                                                }\n                                                                            });\n                                                                            if (!response.ok) {\n                                                                                throw new Error('Failed to sync event date/times');\n                                                                            }\n                                                                            const result = await response.json();\n                                                                            // Store the sync results for display\n                                                                            setSyncResults({\n                                                                                eventsChecked: result.eventsFound || 0,\n                                                                                eventsFixed: result.eventsImported || 0,\n                                                                                eventsSkipped: result.eventsSkipped || 0,\n                                                                                fixedEvents: ((_result_details = result.details) === null || _result_details === void 0 ? void 0 : _result_details.updatedEvents) || [],\n                                                                                timestamp: new Date().toLocaleString()\n                                                                            });\n                                                                            toast({\n                                                                                title: \"Date/Time Sync Complete\",\n                                                                                description: \"Fixed \".concat(result.eventsImported, \" event datetime issues out of \").concat(result.eventsFound, \" events checked.\")\n                                                                            });\n                                                                            // Refresh the validation issues after syncing\n                                                                            refreshData();\n                                                                        } catch (error) {\n                                                                            console.error('Error syncing event date/times:', error);\n                                                                            toast({\n                                                                                title: \"Sync Failed\",\n                                                                                description: \"Failed to sync event date/times. See console for details.\",\n                                                                                variant: \"destructive\"\n                                                                            });\n                                                                        } finally{\n                                                                            setIsSyncing(false);\n                                                                            setLoadingIssues(false);\n                                                                        }\n                                                                    },\n                                                                    children: isSyncing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 945,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Syncing...\"\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 950,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Sync Date/Times\"\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 891,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        loadingIssues ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"py-4 text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-5 w-5 animate-spin mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 959,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Loading event issues...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 960,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 958,\n                                                            columnNumber: 25\n                                                        }, this) : issuesByType.events && issuesByType.events.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHeader, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                                children: \"Name\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 966,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                                children: \"Venue\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 967,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                                children: \"Start Date\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 968,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                                children: \"PostgreSQL ID\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 969,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                                children: \"Issue\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 970,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                                                children: \"Actions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 971,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                        lineNumber: 965,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 964,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                colSpan: 6,\n                                                                                className: \"text-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        size: \"sm\",\n                                                                                        className: \"flex items-center mx-auto mt-2\",\n                                                                                        disabled: isBatchFixing || ((_issuesByType_events = issuesByType.events) === null || _issuesByType_events === void 0 ? void 0 : _issuesByType_events.filter((issue)=>issue.issueType === 'missing_postgres').length) === 0,\n                                                                                        onClick: async ()=>{\n                                                                                            try {\n                                                                                                setIsBatchFixing(true);\n                                                                                                const token = await getAccessToken();\n                                                                                                const response = await fetch(\"\".concat((0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)(), \"/admin/id-validation/batch-fix-events\"), {\n                                                                                                    method: 'POST',\n                                                                                                    headers: {\n                                                                                                        'Content-Type': 'application/json',\n                                                                                                        'Authorization': \"Bearer \".concat(token)\n                                                                                                    }\n                                                                                                });\n                                                                                                if (!response.ok) {\n                                                                                                    throw new Error('Failed to batch fix missing events');\n                                                                                                }\n                                                                                                const result = await response.json();\n                                                                                                // Store the batch fix results for display\n                                                                                                setBatchFixResults({\n                                                                                                    totalIssues: result.totalIssues || 0,\n                                                                                                    successCount: result.successCount || 0,\n                                                                                                    errorCount: result.errorCount || 0,\n                                                                                                    fixedEvents: result.fixedEvents || [],\n                                                                                                    errors: result.errors || [],\n                                                                                                    timestamp: new Date().toLocaleString()\n                                                                                                });\n                                                                                                toast({\n                                                                                                    title: \"Batch Fix Complete\",\n                                                                                                    description: \"Fixed \".concat(result.successCount, \" missing events out of \").concat(result.totalIssues, \" issues.\")\n                                                                                                });\n                                                                                                // Refresh the validation issues after batch fixing\n                                                                                                refreshData();\n                                                                                            } catch (error) {\n                                                                                                console.error('Error batch fixing events:', error);\n                                                                                                toast({\n                                                                                                    title: \"Batch Fix Failed\",\n                                                                                                    description: \"Failed to batch fix missing events. See console for details.\",\n                                                                                                    variant: \"destructive\"\n                                                                                                });\n                                                                                            } finally{\n                                                                                                setIsBatchFixing(false);\n                                                                                                setLoadingIssues(false);\n                                                                                            }\n                                                                                        },\n                                                                                        children: isBatchFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                    lineNumber: 1034,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                \"Fixing...\"\n                                                                                            ]\n                                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                    lineNumber: 1039,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                \"Batch Fix Missing Events\"\n                                                                                            ]\n                                                                                        }, void 0, true)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 979,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    syncResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"mt-4 p-3 bg-muted/50 rounded-md text-sm\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between mb-2\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                        className: \"font-medium flex items-center\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                                                className: \"h-4 w-4 mr-1\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1052,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            \"Sync Results\"\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1051,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"text-xs text-muted-foreground\",\n                                                                                                        children: syncResults.timestamp\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1055,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                lineNumber: 1050,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"grid grid-cols-3 gap-2 mb-3\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-background p-2 rounded\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                                children: \"Checked\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1062,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"font-medium\",\n                                                                                                                children: syncResults.eventsChecked\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1063,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1061,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-background p-2 rounded\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                                children: \"Fixed\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1066,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"font-medium text-green-600\",\n                                                                                                                children: syncResults.eventsFixed\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1067,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1065,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-background p-2 rounded\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                                children: \"Skipped\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1070,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"font-medium\",\n                                                                                                                children: syncResults.eventsSkipped\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1071,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1069,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                lineNumber: 1060,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            syncResults.fixedEvents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs font-medium mb-1\",\n                                                                                                        children: \"Fixed Events:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1077,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                                                                                                        className: \"h-[100px] w-full rounded border p-2 bg-background\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                            className: \"text-xs space-y-1\",\n                                                                                                            children: syncResults.fixedEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                                    className: \"text-muted-foreground\",\n                                                                                                                    children: [\n                                                                                                                        \"• \",\n                                                                                                                        event\n                                                                                                                    ]\n                                                                                                                }, index, true, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                    lineNumber: 1081,\n                                                                                                                    columnNumber: 47\n                                                                                                                }, this))\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                            lineNumber: 1079,\n                                                                                                            columnNumber: 43\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1078,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1049,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    batchFixResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"mt-4 p-3 bg-muted/50 rounded-md text-sm\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between mb-2\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                        className: \"font-medium flex items-center\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                                                className: \"h-4 w-4 mr-1\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1097,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            \"Batch Fix Results\"\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1096,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"text-xs text-muted-foreground\",\n                                                                                                        children: batchFixResults.timestamp\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1100,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                lineNumber: 1095,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"grid grid-cols-3 gap-2 mb-3\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-background p-2 rounded\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                                children: \"Total Issues\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1107,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"font-medium\",\n                                                                                                                children: batchFixResults.totalIssues\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1108,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1106,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-background p-2 rounded\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                                children: \"Fixed\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1111,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"font-medium text-green-600\",\n                                                                                                                children: batchFixResults.successCount\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1112,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1110,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-background p-2 rounded\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                                children: \"Errors\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1115,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"font-medium text-red-600\",\n                                                                                                                children: batchFixResults.errorCount\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                lineNumber: 1116,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1114,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                lineNumber: 1105,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            batchFixResults.fixedEvents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs font-medium mb-1\",\n                                                                                                        children: \"Fixed Events:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1122,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                                                                                                        className: \"h-[100px] w-full rounded border p-2 bg-background\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                            className: \"text-xs space-y-1\",\n                                                                                                            children: batchFixResults.fixedEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                                    className: \"text-muted-foreground\",\n                                                                                                                    children: [\n                                                                                                                        \"• \",\n                                                                                                                        event.name,\n                                                                                                                        \" (ID: \",\n                                                                                                                        event.id,\n                                                                                                                        \")\"\n                                                                                                                    ]\n                                                                                                                }, index, true, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                    lineNumber: 1126,\n                                                                                                                    columnNumber: 47\n                                                                                                                }, this))\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                            lineNumber: 1124,\n                                                                                                            columnNumber: 43\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1123,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true),\n                                                                                            batchFixResults.errors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs font-medium mb-1 mt-3 text-red-600\",\n                                                                                                        children: \"Errors:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1137,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                                                                                                        className: \"h-[100px] w-full rounded border p-2 bg-background\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                            className: \"text-xs space-y-1\",\n                                                                                                            children: batchFixResults.errors.map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                                    className: \"text-red-500\",\n                                                                                                                    children: [\n                                                                                                                        \"• \",\n                                                                                                                        error.event,\n                                                                                                                        \": \",\n                                                                                                                        error.error\n                                                                                                                    ]\n                                                                                                                }, index, true, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                                    lineNumber: 1141,\n                                                                                                                    columnNumber: 47\n                                                                                                                }, this))\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                            lineNumber: 1139,\n                                                                                                            columnNumber: 43\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1138,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1094,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 977,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 976,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        issuesByType.events.map((issue)=>{\n                                                                            var _issue_postgresRecord, _issue_azureRecord, _issue_postgresRecord1, _issue_azureRecord1, _issue_postgresRecord2;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                        className: \"font-medium\",\n                                                                                        children: ((_issue_postgresRecord = issue.postgresRecord) === null || _issue_postgresRecord === void 0 ? void 0 : _issue_postgresRecord.name) || ((_issue_azureRecord = issue.azureRecord) === null || _issue_azureRecord === void 0 ? void 0 : _issue_azureRecord.name) || 'Unknown Event'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1155,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                        children: ((_issue_postgresRecord1 = issue.postgresRecord) === null || _issue_postgresRecord1 === void 0 ? void 0 : _issue_postgresRecord1.venue_name) || ((_issue_azureRecord1 = issue.azureRecord) === null || _issue_azureRecord1 === void 0 ? void 0 : _issue_azureRecord1.venue_name) || 'Unknown Venue'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1156,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                        children: (()=>{\n                                                                                            var _issue_postgresRecord, _issue_azureRecord;\n                                                                                            const dateTime = ((_issue_postgresRecord = issue.postgresRecord) === null || _issue_postgresRecord === void 0 ? void 0 : _issue_postgresRecord.startDateTime) || ((_issue_azureRecord = issue.azureRecord) === null || _issue_azureRecord === void 0 ? void 0 : _issue_azureRecord.startDateTime);\n                                                                                            if (!dateTime) return 'Unknown';\n                                                                                            // MSSQL datetime2 format: \"2025-10-05 00:00:00.0000000\"\n                                                                                            // Extract just the date portion (YYYY-MM-DD) without any timezone conversion\n                                                                                            if (dateTime.includes(' ')) {\n                                                                                                return dateTime.split(' ')[0];\n                                                                                            }\n                                                                                            // Handle ISO format (2025-12-12T00:00:00.000Z) - extract date only\n                                                                                            if (dateTime.includes('T')) {\n                                                                                                return dateTime.split('T')[0];\n                                                                                            }\n                                                                                            // Already just a date\n                                                                                            return dateTime;\n                                                                                        })()\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1157,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                        children: ((_issue_postgresRecord2 = issue.postgresRecord) === null || _issue_postgresRecord2 === void 0 ? void 0 : _issue_postgresRecord2.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                                    className: \"bg-secondary/50 px-1 py-0.5 rounded text-xs\",\n                                                                                                    children: [\n                                                                                                        issue.postgresRecord.id.substring(0, 8),\n                                                                                                        \"...\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                    lineNumber: 1178,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                    variant: \"ghost\",\n                                                                                                    size: \"icon\",\n                                                                                                    className: \"h-6 w-6 ml-1\",\n                                                                                                    onClick: ()=>{\n                                                                                                        navigator.clipboard.writeText(issue.postgresRecord.id);\n                                                                                                        toast({\n                                                                                                            title: \"Copied to clipboard\",\n                                                                                                            description: \"The PostgreSQL ID has been copied to your clipboard.\"\n                                                                                                        });\n                                                                                                    },\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                                        className: \"h-3 w-3\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                        lineNumber: 1193,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                    lineNumber: 1181,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-muted-foreground text-xs italic\",\n                                                                                            children: \"Missing\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1197,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1175,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                                            children: issue.issueType === 'missing_azure' ? 'missing azure' : issue.issueType.replace('_', ' ')\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1201,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1200,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex space-x-2\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                variant: \"outline\",\n                                                                                                size: \"sm\",\n                                                                                                onClick: ()=>handleFixIssue(issue),\n                                                                                                children: \"Fix\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                lineNumber: 1207,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1206,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1205,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, issue.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                lineNumber: 1154,\n                                                                                columnNumber: 31\n                                                                            }, this);\n                                                                        })\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 974,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 963,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"py-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground mb-4\",\n                                                                    children: \"No event ID validation issues found\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 1222,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        className: \"flex items-center mx-auto\",\n                                                                        disabled: isSyncing || isFixingImages,\n                                                                        onClick: async ()=>{\n                                                                            try {\n                                                                                var _result_details;\n                                                                                setIsSyncing(true);\n                                                                                const token = await getAccessToken();\n                                                                                const response = await fetch(\"\".concat((0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)(), \"/admin/events/sync/fix-event-datetimes\"), {\n                                                                                    method: 'POST',\n                                                                                    headers: {\n                                                                                        'Content-Type': 'application/json',\n                                                                                        'Authorization': \"Bearer \".concat(token)\n                                                                                    }\n                                                                                });\n                                                                                if (!response.ok) {\n                                                                                    throw new Error('Failed to sync event date/times');\n                                                                                }\n                                                                                const result = await response.json();\n                                                                                // Store the sync results for display\n                                                                                setSyncResults({\n                                                                                    eventsChecked: result.eventsFound || 0,\n                                                                                    eventsFixed: result.eventsImported || 0,\n                                                                                    eventsSkipped: result.eventsSkipped || 0,\n                                                                                    fixedEvents: ((_result_details = result.details) === null || _result_details === void 0 ? void 0 : _result_details.updatedEvents) || [],\n                                                                                    timestamp: new Date().toLocaleString()\n                                                                                });\n                                                                                toast({\n                                                                                    title: \"Date/Time Sync Complete\",\n                                                                                    description: \"Fixed \".concat(result.eventsImported, \" event datetime issues out of \").concat(result.eventsFound, \" events checked.\")\n                                                                                });\n                                                                                // Refresh the validation issues after syncing\n                                                                                refreshData();\n                                                                            } catch (error) {\n                                                                                console.error('Error syncing event date/times:', error);\n                                                                                toast({\n                                                                                    title: \"Sync Failed\",\n                                                                                    description: \"Failed to sync event date/times. See console for details.\",\n                                                                                    variant: \"destructive\"\n                                                                                });\n                                                                            } finally{\n                                                                                setIsSyncing(false);\n                                                                                setLoadingIssues(false);\n                                                                            }\n                                                                        },\n                                                                        children: isSyncing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1278,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"Syncing...\"\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1283,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                \"Sync Date/Times\"\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                        lineNumber: 1224,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 1223,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                syncResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-4 p-3 bg-muted/50 rounded-md text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-medium flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                            className: \"h-4 w-4 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1295,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        \"Date/Time Sync Results\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1294,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: syncResults.timestamp\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1298,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 1293,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-2 mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Checked\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1305,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: syncResults.eventsChecked\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1306,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1304,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Fixed\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1309,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium text-green-600\",\n                                                                                            children: syncResults.eventsFixed\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1310,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1308,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Skipped\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1313,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: syncResults.eventsSkipped\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1314,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1312,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 1303,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        syncResults.fixedEvents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs font-medium mb-1\",\n                                                                                    children: \"Fixed Events:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1320,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                                                                                    className: \"h-[100px] w-full rounded border p-2 bg-background\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                        className: \"text-xs space-y-1\",\n                                                                                        children: syncResults.fixedEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                className: \"text-muted-foreground\",\n                                                                                                children: [\n                                                                                                    \"• \",\n                                                                                                    event\n                                                                                                ]\n                                                                                            }, index, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                                lineNumber: 1324,\n                                                                                                columnNumber: 41\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                        lineNumber: 1322,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1321,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 1292,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                imageFixResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-4 p-3 bg-muted/50 rounded-md text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-medium flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_Clock_Copy_Database_Image_Loader2_MapPin_MergeIcon_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            className: \"h-4 w-4 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1340,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        \"Image Fix Results\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1339,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: imageFixResults.timestamp\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1343,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 1338,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-4 gap-2 mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Total\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1350,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: imageFixResults.total\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1351,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1349,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Processed\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1354,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: imageFixResults.processed\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1355,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1353,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Success\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1358,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium text-green-600\",\n                                                                                            children: imageFixResults.success\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1359,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1357,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-background p-2 rounded\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: \"Errors\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1362,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium text-red-600\",\n                                                                                            children: imageFixResults.errors\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                            lineNumber: 1363,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                                    lineNumber: 1361,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                            lineNumber: 1348,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                                    lineNumber: 1337,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                            lineNumber: 1221,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                            lineNumber: 795,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 15\n                            }, this),\n                            selectedIssue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fix_dialog__WEBPACK_IMPORTED_MODULE_12__.FixDialog, {\n                                        open: fixDialogOpen,\n                                        setOpen: setFixDialogOpen,\n                                        issue: selectedIssue,\n                                        onFix: async (issue, matchId)=>{\n                                            // Check if the issue has the alreadyHandled flag\n                                            if (issue && 'alreadyHandled' in issue && issue.alreadyHandled === true) {\n                                                // Just refresh the data without calling handleResolveIssue\n                                                await refreshData();\n                                                return;\n                                            }\n                                            // If no matchId is provided and no azureId in the issue, just refresh\n                                            if (!matchId && (!issue || !('azureId' in issue))) {\n                                                await refreshData();\n                                                return;\n                                            }\n                                            // Otherwise, proceed with the normal flow\n                                            await handleResolveIssue((issue === null || issue === void 0 ? void 0 : issue.id) || (selectedIssue === null || selectedIssue === void 0 ? void 0 : selectedIssue.id) || '', 'fix', 'azureId' in issue ? {\n                                                azureId: issue.azureId\n                                            } : matchId || selectedMatchId);\n                                        },\n                                        matchId: selectedMatchId,\n                                        setMatchId: setSelectedMatchId\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                        lineNumber: 1377,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_merge_dialog__WEBPACK_IMPORTED_MODULE_11__.MergeDialog, {\n                                        open: mergeDialogOpen,\n                                        setOpen: setMergeDialogOpen,\n                                        type: selectedIssue.entityType,\n                                        duplicateGroup: {\n                                            ids: [\n                                                selectedIssue.id\n                                            ],\n                                            recordMetadata: [\n                                                {\n                                                    id: selectedIssue.id,\n                                                    name: ((_selectedIssue_postgresRecord = selectedIssue.postgresRecord) === null || _selectedIssue_postgresRecord === void 0 ? void 0 : _selectedIssue_postgresRecord.name) || ((_selectedIssue_azureRecord = selectedIssue.azureRecord) === null || _selectedIssue_azureRecord === void 0 ? void 0 : _selectedIssue_azureRecord.name) || 'Unknown',\n                                                    createdAt: ((_selectedIssue_postgresRecord1 = selectedIssue.postgresRecord) === null || _selectedIssue_postgresRecord1 === void 0 ? void 0 : _selectedIssue_postgresRecord1.createdAt) || ((_selectedIssue_azureRecord1 = selectedIssue.azureRecord) === null || _selectedIssue_azureRecord1 === void 0 ? void 0 : _selectedIssue_azureRecord1.createdAt) || new Date().toISOString(),\n                                                    updatedAt: ((_selectedIssue_postgresRecord2 = selectedIssue.postgresRecord) === null || _selectedIssue_postgresRecord2 === void 0 ? void 0 : _selectedIssue_postgresRecord2.updatedAt) || ((_selectedIssue_azureRecord2 = selectedIssue.azureRecord) === null || _selectedIssue_azureRecord2 === void 0 ? void 0 : _selectedIssue_azureRecord2.updatedAt) || new Date().toISOString()\n                                                }\n                                            ]\n                                        },\n                                        onMerge: (primaryId, duplicateIds)=>{\n                                            handleResolveIssue(selectedIssue.id, \"merge\", primaryId);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                                        lineNumber: 1406,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n                lineNumber: 507,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\id-validation\\\\index.tsx\",\n        lineNumber: 495,\n        columnNumber: 5\n    }, this);\n}\n_s(IdValidation, \"5p7sltQSwLWSfzkctYpSiHipCQk=\", false, function() {\n    return [\n        _hooks_use_auth__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter\n    ];\n});\n_c = IdValidation;\nvar _c;\n$RefreshReg$(_c, \"IdValidation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/id-validation/index.tsx\n"));

/***/ })

});