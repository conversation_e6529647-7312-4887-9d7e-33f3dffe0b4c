"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/admin/event-relations/venue.tsx":
/*!****************************************************!*\
  !*** ./components/admin/event-relations/venue.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventVenueRelations: () => (/* binding */ EventVenueRelations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-auth */ \"(app-pages-browser)/./hooks/use-auth.ts\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ EventVenueRelations auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction EventVenueRelations() {\n    _s();\n    const [counts, setCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [missingEvents, setMissingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mismatchedEvents, setMismatchedEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [batchFixing, setBatchFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [csvFixing, setCsvFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [batchFixProgress, setBatchFixProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 0,\n        total: 0,\n        success: 0,\n        failed: 0\n    });\n    const [fixingEvents, setFixingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        missing: false,\n        mismatched: false\n    });\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { getAccessToken } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // Fetch data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventVenueRelations.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"EventVenueRelations.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            setIsLoading(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Fetch counts\n            const countsResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!countsResponse.ok) {\n                throw new Error('Failed to fetch event-venue relationship counts');\n            }\n            const countsData = await countsResponse.json();\n            setCounts(countsData);\n            // Fetch missing events\n            const missingResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/missing\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (missingResponse.ok) {\n                const missingData = await missingResponse.json();\n                setMissingEvents(missingData);\n            }\n            // Fetch mismatched events\n            const mismatchedResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/mismatched\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (mismatchedResponse.ok) {\n                const mismatchedData = await mismatchedResponse.json();\n                setMismatchedEvents(mismatchedData);\n            }\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching event-venue relationship data', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch event-venue relationship data');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleExpanded = (section)=>{\n        setExpanded((prev)=>({\n                ...prev,\n                [section]: !prev[section]\n            }));\n    };\n    // Handle batch fixing a limited number of events\n    const handleBatchFix = async ()=>{\n        try {\n            setBatchFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Determine which events to fix - prioritize missing events\n            const eventsToFix = [\n                ...missingEvents\n            ].slice(0, 10); // Limit to 10 events at a time\n            if (eventsToFix.length === 0) {\n                toast({\n                    title: 'No Events to Fix',\n                    description: 'There are no events that need fixing.',\n                    variant: 'default'\n                });\n                return;\n            }\n            // Set up progress tracking\n            setBatchFixProgress({\n                current: 0,\n                total: eventsToFix.length,\n                success: 0,\n                failed: 0\n            });\n            // Mark all events as being fixed\n            const newFixingState = {};\n            eventsToFix.forEach((event)=>{\n                newFixingState[event.id] = true;\n            });\n            setFixingEvents((prev)=>({\n                    ...prev,\n                    ...newFixingState\n                }));\n            console.log(\"\\uD83D\\uDD27 [Frontend] Starting batch fix for \".concat(eventsToFix.length, \" venue relationships\"));\n            // Process events sequentially to avoid overwhelming the server\n            for(let i = 0; i < eventsToFix.length; i++){\n                const event = eventsToFix[i];\n                setBatchFixProgress((prev)=>({\n                        ...prev,\n                        current: i + 1\n                    }));\n                try {\n                    console.log(\"\\uD83D\\uDD27 [Frontend] Fixing event \".concat(i + 1, \"/\").concat(eventsToFix.length, \": \").concat(event.name, \" (ID: \").concat(event.id, \")\"));\n                    // Create the request body with eventId and venueId\n                    const requestBody = {\n                        eventId: event.id,\n                        venueId: event.azure_venue_id // Using the Azure venue ID from the selected event\n                    };\n                    const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix\"), {\n                        method: 'POST',\n                        headers: {\n                            'Authorization': \"Bearer \".concat(token),\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify(requestBody)\n                    });\n                    if (!response.ok) {\n                        console.error(\"\\uD83D\\uDD27 [Frontend] Failed to fix event \".concat(event.name, \": \").concat(response.status));\n                        setBatchFixProgress((prev)=>({\n                                ...prev,\n                                failed: prev.failed + 1\n                            }));\n                    } else {\n                        console.log(\"\\uD83D\\uDD27 [Frontend] Successfully fixed event \".concat(event.name));\n                        setBatchFixProgress((prev)=>({\n                                ...prev,\n                                success: prev.success + 1\n                            }));\n                    }\n                } catch (eventError) {\n                    console.error(\"\\uD83D\\uDD27 [Frontend] Error fixing event \".concat(event.name, \":\"), eventError);\n                    setBatchFixProgress((prev)=>({\n                            ...prev,\n                            failed: prev.failed + 1\n                        }));\n                } finally{\n                    // Mark this event as no longer being fixed\n                    setFixingEvents((prev)=>({\n                            ...prev,\n                            [event.id]: false\n                        }));\n                }\n                // Small delay to avoid overwhelming the server\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n            }\n            toast({\n                title: 'Batch Fix Complete',\n                description: \"Successfully fixed \".concat(batchFixProgress.success, \" out of \").concat(eventsToFix.length, \" venue relationships.\"),\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error during batch fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete batch fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setBatchFixing(false);\n            // Reset progress\n            setBatchFixProgress({\n                current: 0,\n                total: 0,\n                success: 0,\n                failed: 0\n            });\n        }\n    };\n    // Handle CSV-based fix for all event-venue relationships\n    const handleCsvFix = async ()=>{\n        try {\n            var _result_stats;\n            setCsvFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            console.log('🔧 [Frontend] Starting CSV-based fix for event-venue relationships');\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix-from-csv\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('🔧 [Frontend] Server error response:', errorText);\n                throw new Error(\"Failed to execute CSV fix: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('🔧 [Frontend] CSV fix completed:', result);\n            toast({\n                title: 'CSV Fix Complete',\n                description: \"Validated \".concat(((_result_stats = result.stats) === null || _result_stats === void 0 ? void 0 : _result_stats.validatedRelationships) || 0, \" venue relationships from CSV\"),\n                variant: 'default'\n            });\n            // Refresh data after CSV fix\n            fetchData();\n        } catch (err) {\n            console.error('🔧 [Frontend] Error during CSV fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete CSV fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setCsvFixing(false);\n        }\n    };\n    // Open confirmation dialog for fixing an event\n    const openFixConfirmDialog = (event)=>{\n        setSelectedEvent(event);\n        setConfirmDialogOpen(true);\n    };\n    // Handle fixing a single event's venue relationship through the new confirmation dialog\n    const handleFixEventVenue = async ()=>{\n        if (!selectedEvent) return;\n        try {\n            // Close the dialog\n            setConfirmDialogOpen(false);\n            // Mark this event as being fixed\n            setFixingEvents((prev)=>({\n                    ...prev,\n                    [selectedEvent.id]: true\n                }));\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Create the request body with eventId and venueId\n            const requestBody = {\n                eventId: selectedEvent.id,\n                venueId: selectedEvent.azure_venue_id // Using the Azure venue ID from the selected event\n            };\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to fix event-venue relationship for \".concat(selectedEvent.name));\n            }\n            await response.json();\n            toast({\n                title: 'Success',\n                description: 'Event \"'.concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name, '\" venue updated to \"').concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.azure_venue_name, '\"'),\n                variant: 'default'\n            });\n            // Refresh data after successful fix\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing event-venue relationship', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : \"Failed to fix event-venue relationship for \".concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name),\n                variant: 'destructive'\n            });\n        } finally{\n            // Clear the fixing state for this event\n            if (selectedEvent) {\n                setFixingEvents((prev)=>({\n                        ...prev,\n                        [selectedEvent.id]: false\n                    }));\n            }\n            // Clear the selected event\n            setSelectedEvent(null);\n        }\n    };\n    // Determine status message and icon color\n    const getStatusDetails = ()=>{\n        if (!counts) return {\n            message: 'Loading...',\n            status: 'neutral'\n        };\n        // Check if there are any actual problematic relationships in the dropdown lists\n        const hasProblematicRelations = missingEvents.length > 0 || mismatchedEvents.length > 0;\n        // If there are no problematic relations to troubleshoot, show green\n        // Only show yellow warning when there's something that needs attention\n        if (!hasProblematicRelations) {\n            return {\n                message: counts.missingVenues === 0 ? 'All events have venue relationships' : \"\".concat(counts.missingVenues, \" events with no venue relations\"),\n                status: 'good'\n            };\n        }\n        // Show yellow warning when there are items in the dropdown lists that need attention\n        return {\n            message: \"\".concat(counts.missingVenues, \" events with no venue relations\"),\n            status: 'warning'\n        };\n    };\n    // Get the appropriate icon color based on status\n    const getIconColorClass = (status)=>{\n        switch(status){\n            case 'good':\n                return 'text-green-500';\n            case 'warning':\n                return 'text-yellow-500';\n            case 'bad':\n                return 'text-red-500';\n            default:\n                return 'text-muted-foreground';\n        }\n    };\n    const statusDetails = getStatusDetails();\n    // Format date for display\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'No date';\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"flex flex-row items-center space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-5 w-5 mr-2 \".concat(getIconColorClass(statusDetails.status))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-xl\",\n                            children: \"Events - Venues\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                lineNumber: 412,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-8 w-8 animate-spin text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-muted-foreground\",\n                                children: \"Loading relationship data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 11\n                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8 px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5 text-red-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-500\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: statusDetails.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            onClick: handleBatchFix,\n                                            disabled: batchFixing || csvFixing || missingEvents.length === 0,\n                                            className: \"flex items-center\",\n                                            variant: \"outline\",\n                                            children: batchFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Fixing \",\n                                                    batchFixProgress.current,\n                                                    \"/\",\n                                                    batchFixProgress.total,\n                                                    \"...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Batch Fix (10 Events)\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            onClick: handleCsvFix,\n                                            disabled: batchFixing || csvFixing,\n                                            className: \"flex items-center\",\n                                            variant: \"outline\",\n                                            children: csvFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Validating CSV...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Validate from CSV\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"icon\",\n                                            onClick: fetchData,\n                                            disabled: isLoading || batchFixing || csvFixing,\n                                            title: \"Refresh data\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, this),\n                                batchFixing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-muted rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm mb-1 flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Batch Fix Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        batchFixProgress.current,\n                                                        \"/\",\n                                                        batchFixProgress.total,\n                                                        \" events\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-secondary h-2 rounded-full overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary h-full transition-all duration-300 ease-in-out\",\n                                                style: {\n                                                    width: \"\".concat(batchFixProgress.current / Math.max(batchFixProgress.total, 1) * 100, \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between text-xs mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500\",\n                                                    children: [\n                                                        batchFixProgress.success,\n                                                        \" successful\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: [\n                                                        batchFixProgress.failed,\n                                                        \" failed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                            open: expanded.missing,\n                                            onOpenChange: ()=>toggleExpanded('missing'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Events Missing Venue Relationships (\",\n                                                                        missingEvents.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 556,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        expanded.missing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: missingEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No events missing venues\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Event Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Azure Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"PostgreSQL Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 575,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                                children: missingEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"font-medium\",\n                                                                                children: event.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 582,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: formatDate(event.date)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 583,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: event.azure_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 584,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"text-red-500\",\n                                                                                children: event.postgres_venue_name || 'Missing'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 585,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>openFixConfirmDialog(event),\n                                                                                    disabled: fixingEvents[event.id],\n                                                                                    children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                                lineNumber: 595,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            \"Fixing...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Fix\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                    lineNumber: 587,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 586,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, event.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 581,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                            open: expanded.mismatched,\n                                            onOpenChange: ()=>toggleExpanded('mismatched'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Events with Mismatched Venue (\",\n                                                                        mismatchedEvents.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 620,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        expanded.mismatched ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 625,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: mismatchedEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No events with mismatched venues\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Event Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 636,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 637,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Azure Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 638,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"PostgreSQL Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 639,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 640,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                                children: mismatchedEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"font-medium\",\n                                                                                children: event.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 646,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: formatDate(event.date)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 647,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: event.azure_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 648,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"text-yellow-500\",\n                                                                                children: event.postgres_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 649,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>openFixConfirmDialog(event),\n                                                                                    disabled: fixingEvents[event.id],\n                                                                                    children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                                lineNumber: 659,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            \"Fixing...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Fix\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                    lineNumber: 651,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 650,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, event.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 645,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                        open: confirmDialogOpen,\n                        onOpenChange: setConfirmDialogOpen,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                            children: \"Fix Event-Venue Relationship\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                                            children: selectedEvent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Are you sure you want to fix the venue relationship for this event?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-muted p-3 rounded-md text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Event:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Date:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formatDate((selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.date) || '')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Current Venue:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    (selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.postgres_venue_name) || 'None'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Target Venue:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 693,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.azure_venue_name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"This will:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc pl-5 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Update the event to reference the correct venue from Azure\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 698,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Create the venue in PostgreSQL if it doesn't exist\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Update the event's venue_id to maintain data consistency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 684,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setConfirmDialogOpen(false),\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            onClick: handleFixEventVenue,\n                                            children: \"Fix\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n        lineNumber: 411,\n        columnNumber: 5\n    }, this);\n}\n_s(EventVenueRelations, \"+xzI3q3Xr/UWzLjNK+wCfOIM5Iw=\", false, function() {\n    return [\n        _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = EventVenueRelations;\nvar _c;\n$RefreshReg$(_c, \"EventVenueRelations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/event-relations/venue.tsx\n"));

/***/ })

});