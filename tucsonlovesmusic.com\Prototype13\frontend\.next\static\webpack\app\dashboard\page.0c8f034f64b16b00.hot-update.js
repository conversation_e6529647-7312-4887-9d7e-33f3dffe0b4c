"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/admin/event-relations/venue.tsx":
/*!****************************************************!*\
  !*** ./components/admin/event-relations/venue.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventVenueRelations: () => (/* binding */ EventVenueRelations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Check,ChevronDown,ChevronRight,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-auth */ \"(app-pages-browser)/./hooks/use-auth.ts\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ EventVenueRelations auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction EventVenueRelations() {\n    _s();\n    const [counts, setCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [missingEvents, setMissingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mismatchedEvents, setMismatchedEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [batchFixing, setBatchFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [csvFixing, setCsvFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [batchFixProgress, setBatchFixProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 0,\n        total: 0,\n        success: 0,\n        failed: 0\n    });\n    const [fixingEvents, setFixingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        missing: false,\n        mismatched: false\n    });\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { getAccessToken } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // Fetch data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventVenueRelations.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"EventVenueRelations.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            setIsLoading(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Fetch counts\n            const countsResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!countsResponse.ok) {\n                throw new Error('Failed to fetch event-venue relationship counts');\n            }\n            const countsData = await countsResponse.json();\n            setCounts(countsData);\n            // Fetch missing events\n            const missingResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/missing\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (missingResponse.ok) {\n                const missingData = await missingResponse.json();\n                setMissingEvents(missingData);\n            }\n            // Fetch mismatched events\n            const mismatchedResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/mismatched\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (mismatchedResponse.ok) {\n                const mismatchedData = await mismatchedResponse.json();\n                setMismatchedEvents(mismatchedData);\n            }\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching event-venue relationship data', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch event-venue relationship data');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleExpanded = (section)=>{\n        setExpanded((prev)=>({\n                ...prev,\n                [section]: !prev[section]\n            }));\n    };\n    // Handle batch fixing a limited number of events\n    const handleBatchFix = async ()=>{\n        try {\n            setBatchFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Determine which events to fix - prioritize missing events\n            const eventsToFix = [\n                ...missingEvents\n            ].slice(0, 10); // Limit to 10 events at a time\n            if (eventsToFix.length === 0) {\n                toast({\n                    title: 'No Events to Fix',\n                    description: 'There are no events that need fixing.',\n                    variant: 'default'\n                });\n                return;\n            }\n            // Set up progress tracking\n            setBatchFixProgress({\n                current: 0,\n                total: eventsToFix.length,\n                success: 0,\n                failed: 0\n            });\n            // Mark all events as being fixed\n            const newFixingState = {};\n            eventsToFix.forEach((event)=>{\n                newFixingState[event.id] = true;\n            });\n            setFixingEvents((prev)=>({\n                    ...prev,\n                    ...newFixingState\n                }));\n            console.log(\"\\uD83D\\uDD27 [Frontend] Starting batch fix for \".concat(eventsToFix.length, \" venue relationships\"));\n            // Track success and failure counts locally to avoid state update timing issues\n            let successCount = 0;\n            let failureCount = 0;\n            // Process events sequentially to avoid overwhelming the server\n            for(let i = 0; i < eventsToFix.length; i++){\n                const event = eventsToFix[i];\n                setBatchFixProgress((prev)=>({\n                        ...prev,\n                        current: i + 1\n                    }));\n                try {\n                    console.log(\"\\uD83D\\uDD27 [Frontend] Fixing event \".concat(i + 1, \"/\").concat(eventsToFix.length, \": \").concat(event.name, \" (ID: \").concat(event.id, \")\"));\n                    // Create the request body with eventId and venueId\n                    const requestBody = {\n                        eventId: event.id,\n                        venueId: event.azure_venue_id // Using the Azure venue ID from the selected event\n                    };\n                    const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix\"), {\n                        method: 'POST',\n                        headers: {\n                            'Authorization': \"Bearer \".concat(token),\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify(requestBody)\n                    });\n                    if (!response.ok) {\n                        console.error(\"\\uD83D\\uDD27 [Frontend] Failed to fix event \".concat(event.name, \": \").concat(response.status));\n                        failureCount++;\n                        setBatchFixProgress((prev)=>({\n                                ...prev,\n                                failed: prev.failed + 1\n                            }));\n                    } else {\n                        console.log(\"\\uD83D\\uDD27 [Frontend] Successfully fixed event \".concat(event.name));\n                        successCount++;\n                        setBatchFixProgress((prev)=>({\n                                ...prev,\n                                success: prev.success + 1\n                            }));\n                    }\n                } catch (eventError) {\n                    console.error(\"\\uD83D\\uDD27 [Frontend] Error fixing event \".concat(event.name, \":\"), eventError);\n                    failureCount++;\n                    setBatchFixProgress((prev)=>({\n                            ...prev,\n                            failed: prev.failed + 1\n                        }));\n                } finally{\n                    // Mark this event as no longer being fixed\n                    setFixingEvents((prev)=>({\n                            ...prev,\n                            [event.id]: false\n                        }));\n                }\n                // Small delay to avoid overwhelming the server\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n            }\n            // Capture the final success count before resetting state\n            const finalSuccessCount = batchFixProgress.success;\n            toast({\n                title: 'Batch Fix Complete',\n                description: \"Successfully fixed \".concat(finalSuccessCount, \" out of \").concat(eventsToFix.length, \" venue relationships.\"),\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error during batch fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete batch fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setBatchFixing(false);\n            // Reset progress\n            setBatchFixProgress({\n                current: 0,\n                total: 0,\n                success: 0,\n                failed: 0\n            });\n        }\n    };\n    // Handle CSV-based fix for all event-venue relationships\n    const handleCsvFix = async ()=>{\n        try {\n            var _result_stats;\n            setCsvFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            console.log('🔧 [Frontend] Starting CSV-based fix for event-venue relationships');\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix-from-csv\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('🔧 [Frontend] Server error response:', errorText);\n                throw new Error(\"Failed to execute CSV fix: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('🔧 [Frontend] CSV fix completed:', result);\n            toast({\n                title: 'CSV Fix Complete',\n                description: \"Validated \".concat(((_result_stats = result.stats) === null || _result_stats === void 0 ? void 0 : _result_stats.validatedRelationships) || 0, \" venue relationships from CSV\"),\n                variant: 'default'\n            });\n            // Refresh data after CSV fix\n            fetchData();\n        } catch (err) {\n            console.error('🔧 [Frontend] Error during CSV fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete CSV fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setCsvFixing(false);\n        }\n    };\n    // Open confirmation dialog for fixing an event\n    const openFixConfirmDialog = (event)=>{\n        setSelectedEvent(event);\n        setConfirmDialogOpen(true);\n    };\n    // Handle fixing a single event's venue relationship through the new confirmation dialog\n    const handleFixEventVenue = async ()=>{\n        if (!selectedEvent) return;\n        try {\n            // Close the dialog\n            setConfirmDialogOpen(false);\n            // Mark this event as being fixed\n            setFixingEvents((prev)=>({\n                    ...prev,\n                    [selectedEvent.id]: true\n                }));\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Create the request body with eventId and venueId\n            const requestBody = {\n                eventId: selectedEvent.id,\n                venueId: selectedEvent.azure_venue_id // Using the Azure venue ID from the selected event\n            };\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-venue-relations/fix\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to fix event-venue relationship for \".concat(selectedEvent.name));\n            }\n            await response.json();\n            toast({\n                title: 'Success',\n                description: 'Event \"'.concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name, '\" venue updated to \"').concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.azure_venue_name, '\"'),\n                variant: 'default'\n            });\n            // Refresh data after successful fix\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing event-venue relationship', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : \"Failed to fix event-venue relationship for \".concat(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name),\n                variant: 'destructive'\n            });\n        } finally{\n            // Clear the fixing state for this event\n            if (selectedEvent) {\n                setFixingEvents((prev)=>({\n                        ...prev,\n                        [selectedEvent.id]: false\n                    }));\n            }\n            // Clear the selected event\n            setSelectedEvent(null);\n        }\n    };\n    // Determine status message and icon color\n    const getStatusDetails = ()=>{\n        if (!counts) return {\n            message: 'Loading...',\n            status: 'neutral'\n        };\n        // Check if there are any actual problematic relationships in the dropdown lists\n        const hasProblematicRelations = missingEvents.length > 0 || mismatchedEvents.length > 0;\n        // If there are no problematic relations to troubleshoot, show green\n        // Only show yellow warning when there's something that needs attention\n        if (!hasProblematicRelations) {\n            return {\n                message: counts.missingVenues === 0 ? 'All events have venue relationships' : \"\".concat(counts.missingVenues, \" events with no venue relations\"),\n                status: 'good'\n            };\n        }\n        // Show yellow warning when there are items in the dropdown lists that need attention\n        return {\n            message: \"\".concat(counts.missingVenues, \" events with no venue relations\"),\n            status: 'warning'\n        };\n    };\n    // Get the appropriate icon color based on status\n    const getIconColorClass = (status)=>{\n        switch(status){\n            case 'good':\n                return 'text-green-500';\n            case 'warning':\n                return 'text-yellow-500';\n            case 'bad':\n                return 'text-red-500';\n            default:\n                return 'text-muted-foreground';\n        }\n    };\n    const statusDetails = getStatusDetails();\n    // Format date for display\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'No date';\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"flex flex-row items-center space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-5 w-5 mr-2 \".concat(getIconColorClass(statusDetails.status))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-xl\",\n                            children: \"Events - Venues\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-8 w-8 animate-spin text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-muted-foreground\",\n                                children: \"Loading relationship data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8 px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5 text-red-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-500\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: statusDetails.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            onClick: handleBatchFix,\n                                            disabled: batchFixing || csvFixing || missingEvents.length === 0,\n                                            className: \"flex items-center\",\n                                            variant: \"outline\",\n                                            children: batchFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Fixing \",\n                                                    batchFixProgress.current,\n                                                    \"/\",\n                                                    batchFixProgress.total,\n                                                    \"...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Batch Fix (10 Events)\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            onClick: handleCsvFix,\n                                            disabled: batchFixing || csvFixing,\n                                            className: \"flex items-center\",\n                                            variant: \"outline\",\n                                            children: csvFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Validating CSV...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Validate from CSV\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"icon\",\n                                            onClick: fetchData,\n                                            disabled: isLoading || batchFixing || csvFixing,\n                                            title: \"Refresh data\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this),\n                                batchFixing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-muted rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm mb-1 flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Batch Fix Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        batchFixProgress.current,\n                                                        \"/\",\n                                                        batchFixProgress.total,\n                                                        \" events\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-secondary h-2 rounded-full overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary h-full transition-all duration-300 ease-in-out\",\n                                                style: {\n                                                    width: \"\".concat(batchFixProgress.current / Math.max(batchFixProgress.total, 1) * 100, \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between text-xs mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500\",\n                                                    children: [\n                                                        batchFixProgress.success,\n                                                        \" successful\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: [\n                                                        batchFixProgress.failed,\n                                                        \" failed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                            open: expanded.missing,\n                                            onOpenChange: ()=>toggleExpanded('missing'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Events Missing Venue Relationships (\",\n                                                                        missingEvents.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        expanded.missing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: missingEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No events missing venues\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Event Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Azure Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"PostgreSQL Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 586,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                                children: missingEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"font-medium\",\n                                                                                children: event.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 592,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: formatDate(event.date)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 593,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: event.azure_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 594,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"text-red-500\",\n                                                                                children: event.postgres_venue_name || 'Missing'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 595,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>openFixConfirmDialog(event),\n                                                                                    disabled: fixingEvents[event.id],\n                                                                                    children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                                lineNumber: 605,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            \"Fixing...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Fix\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                    lineNumber: 597,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 596,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, event.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                            open: expanded.mismatched,\n                                            onOpenChange: ()=>toggleExpanded('mismatched'),\n                                            className: \"border rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                    className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 629,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Events with Mismatched Venue (\",\n                                                                        mismatchedEvents.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 630,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        expanded.mismatched ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                            lineNumber: 635,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                    className: \"px-4 pb-4\",\n                                                    children: mismatchedEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground py-2\",\n                                                        children: \"No events with mismatched venues\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Event Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 646,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 647,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Azure Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 648,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"PostgreSQL Venue\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 649,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                            children: \"Actions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                            lineNumber: 650,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                    lineNumber: 645,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                                children: mismatchedEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"font-medium\",\n                                                                                children: event.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 656,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: formatDate(event.date)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 657,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: event.azure_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 658,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                className: \"text-yellow-500\",\n                                                                                children: event.postgres_venue_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 659,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>openFixConfirmDialog(event),\n                                                                                    disabled: fixingEvents[event.id],\n                                                                                    children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Check_ChevronDown_ChevronRight_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                                lineNumber: 669,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            \"Fixing...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Fix\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                    lineNumber: 661,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                                lineNumber: 660,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, event.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 655,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                    lineNumber: 639,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                        open: confirmDialogOpen,\n                        onOpenChange: setConfirmDialogOpen,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                            children: \"Fix Event-Venue Relationship\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                                            children: selectedEvent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Are you sure you want to fix the venue relationship for this event?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-muted p-3 rounded-md text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Event:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 700,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Date:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 701,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formatDate((selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.date) || '')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 701,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Current Venue:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 702,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    (selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.postgres_venue_name) || 'None'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 702,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Target Venue:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                        lineNumber: 703,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.azure_venue_name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 703,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"This will:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc pl-5 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Update the event to reference the correct venue from Azure\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Create the venue in PostgreSQL if it doesn't exist\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Update the event's venue_id to maintain data consistency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setConfirmDialogOpen(false),\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            onClick: handleFixEventVenue,\n                                            children: \"Fix\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                            lineNumber: 691,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                        lineNumber: 690,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\venue.tsx\",\n        lineNumber: 421,\n        columnNumber: 5\n    }, this);\n}\n_s(EventVenueRelations, \"+xzI3q3Xr/UWzLjNK+wCfOIM5Iw=\", false, function() {\n    return [\n        _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = EventVenueRelations;\nvar _c;\n$RefreshReg$(_c, \"EventVenueRelations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/event-relations/venue.tsx\n"));

/***/ })

});