"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { 
  GitMerge, 
  Loader2, 
  AlertCircle, 
  Calendar, 
  ChevronDown, 
  ChevronRight, 
  Check,
  Zap,
  Wrench,
  RefreshCw,
  FileText  // Add this import
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { getApiUrl } from "@/lib/config";

interface EventMissingTalent {
  id: string;
  name: string;
  date: string;
  venue_name: string;
  azure_talent_ids: string[];
  postgres_talent_count: number;
}

interface EventTalentStatusCounts {
  total: number;
  missingTalents: number;
  potentialFixes: number;
}

export function EventTalentRelations() {
  const [counts, setCounts] = useState<EventTalentStatusCounts | null>(null);
  const [missingEvents, setMissingEvents] = useState<EventMissingTalent[]>([]);
  const [mismatchedEvents, setMismatchedEvents] = useState<EventMissingTalent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isBulkFixing, setIsBulkFixing] = useState(false);
  const [batchFixing, setBatchFixing] = useState(false);
  const [csvFixing, setCsvFixing] = useState(false);  // Add this line
  const [batchFixProgress, setBatchFixProgress] = useState({ current: 0, total: 0, success: 0, failed: 0 });
  const [fixingEvents, setFixingEvents] = useState<Record<string, boolean>>({}); // Track which events are being fixed
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState<{ missing: boolean; mismatched: boolean }>({ 
    missing: false, 
    mismatched: false
  });
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<EventMissingTalent | null>(null);
  const { getAccessToken } = useAuth();
  const { toast } = useToast();

// Handle CSV-based fix for all event-talent relationships
const handleCsvFix = async () => {
  try {
    setCsvFixing(true);
    const token = await getAccessToken();
    const baseUrl = getApiUrl();
    
    console.log('🔧 [Frontend] Starting CSV-based fix for event-talent relationships');
    
    const response = await fetch(`${baseUrl}/admin/event-talent-relations/fix-from-csv`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('🔧 [Frontend] Server error response:', errorText);
      throw new Error(`Failed to execute CSV fix: ${response.status} ${response.statusText}`);
    }
    
    const result = await response.json();
    console.log('🔧 [Frontend] CSV fix completed successfully:', result);
    
    toast({
      title: 'CSV Fix Complete',
      description: result.message || 'Successfully processed event-talent relationships from CSV.',
      variant: 'default'
    });
    
    // Refresh data to show updated counts
    fetchData();
  } catch (err: unknown) {
    console.error('🔧 [Frontend] Error during CSV fix operation', err);
    
    toast({
      title: 'Error',
      description: err instanceof Error ? err.message : 'Failed to complete CSV fix operation',
      variant: 'destructive'
    });
  } finally {
    setCsvFixing(false);
  }
};

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const token = await getAccessToken();
      const baseUrl = getApiUrl();

      // Only fetch basic counts - avoid expensive missing/mismatched queries
      const countsResponse = await fetch(`${baseUrl}/admin/event-talent-relations?year=2025`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!countsResponse.ok) {
        throw new Error('Failed to fetch event-talent relationship counts');
      }

      const countsData = await countsResponse.json();
      setCounts(countsData);

      // Skip the expensive missing/mismatched queries on initial load
      // These will only be loaded when the user expands the sections
      setMissingEvents([]);
      setMismatchedEvents([]);

      setError(null);
    } catch (err: unknown) {
      console.error('Error fetching event-talent relationship data', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch event-talent relationship data');
    } finally {
      setIsLoading(false);
    }
  };

  // Load expensive data only when sections are expanded
  const loadSectionData = async (section: 'missing' | 'mismatched') => {
    try {
      const token = await getAccessToken();
      const baseUrl = getApiUrl();

      if (section === 'missing' && missingEvents.length === 0) {
        console.log('Loading missing events data...');
        const missingResponse = await fetch(`${baseUrl}/admin/event-talent-relations/missing?year=2025`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (missingResponse.ok) {
          const missingData = await missingResponse.json();
          setMissingEvents(missingData);
        }
      }

      if (section === 'mismatched' && mismatchedEvents.length === 0) {
        console.log('Loading mismatched events data...');
        const mismatchedResponse = await fetch(`${baseUrl}/admin/event-talent-relations/mismatched?year=2025`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (mismatchedResponse.ok) {
          const mismatchedData = await mismatchedResponse.json();
          setMismatchedEvents(mismatchedData);
        }
      }
    } catch (err: unknown) {
      console.error(`Error loading ${section} events data`, err);
      toast({
        title: 'Error',
        description: `Failed to load ${section} events data`,
        variant: 'destructive'
      });
    }
  };

  const toggleExpanded = async (section: 'missing' | 'mismatched') => {
    const isCurrentlyExpanded = expanded[section];

    // If expanding and no data loaded yet, load it first
    if (!isCurrentlyExpanded) {
      await loadSectionData(section);
    }

    setExpanded(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleFixAll = async () => {
    try {
      setIsBulkFixing(true);
      const token = await getAccessToken();
      const baseUrl = getApiUrl();
      
      // Pass year=2025 to match the same filter used in fetchData() for event display
      const response = await fetch(`${baseUrl}/admin/event-talent-relations/fix-all-aggregated?year=2025`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fix all event-talent relationships');
      }
      
      const result = await response.json();
      
      toast({
        title: 'Success',
        description: result.message,
        variant: 'default'
      });
      
      // Refresh data
      fetchData();
    } catch (err: unknown) {
      console.error('Error fixing all event-talent relationships', err);
      
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to fix all event-talent relationships',
        variant: 'destructive'
      });
    } finally {
      setIsBulkFixing(false);
    }
  };

  // Handle batch fixing using the bulk fix endpoint for better performance
  const handleBatchFix = async () => {
    try {
      setBatchFixing(true);
      const token = await getAccessToken();
      const baseUrl = getApiUrl();
      
      // Check if there are events that need fixing
      if (missingEvents.length === 0 && mismatchedEvents.length === 0) {
        toast({
          title: 'No Events to Fix',
          description: 'There are no events that need fixing.',
          variant: 'default'
        });
        return;
      }
      
      console.log(`🔧 [Frontend] Starting bulk batch fix for all events`);
      
      // Use the new aggregated batch fix endpoint which calls individual fixes for each event
      // IMPORTANT: Pass the same year filter (2025) that's used for displaying events in fetchData()
      // This ensures the batch fix processes the exact same events shown in the UI
      const response = await fetch(`${baseUrl}/admin/event-talent-relations/fix-all-aggregated?year=2025`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to execute batch fix: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      
      console.log(`🔧 [Frontend] Batch fix completed successfully`);
      
      toast({
        title: 'Batch Fix Complete',
        description: result.message || 'Successfully completed batch fix operation.',
        variant: 'default'
      });
      
      // Refresh data to show updated counts
      fetchData();
    } catch (err: unknown) {
      console.error('Error during batch fix operation', err);
      
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to complete batch fix operation',
        variant: 'destructive'
      });
    } finally {
      setBatchFixing(false);
    }
  };

  // Open confirmation dialog for fixing an event
  const openFixConfirmDialog = (event: EventMissingTalent) => {
    setSelectedEvent(event);
    setConfirmDialogOpen(true);
  };

  // Handle fixing a single event's talent relationships
  const handleFixSingleEvent = async () => {
    if (!selectedEvent) return;
    
    try {
      // Close the dialog
      setConfirmDialogOpen(false);
      
      // Mark this event as being fixed
      setFixingEvents(prev => ({ ...prev, [selectedEvent.id]: true }));
      
      console.log(`🔧 [Frontend] Starting fix for event: ${selectedEvent.name} (ID: ${selectedEvent.id})`);
      console.log(`🔧 [Frontend] Event details:`, {
        id: selectedEvent.id,
        name: selectedEvent.name,
        azure_talent_ids: selectedEvent.azure_talent_ids,
        postgres_talent_count: selectedEvent.postgres_talent_count
      });
      
      const token = await getAccessToken();
      const baseUrl = getApiUrl();
      const url = `${baseUrl}/admin/event-talent-relations/fix/${selectedEvent.id}`;
      
      console.log(`🔧 [Frontend] Making POST request to: ${url}`);
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`🔧 [Frontend] Response status: ${response.status} ${response.statusText}`);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`🔧 [Frontend] Server error response:`, errorText);
        throw new Error(`Failed to fix event-talent relationship for ${selectedEvent.name}: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      console.log(`🔧 [Frontend] Success response:`, result);
      
      toast({
        title: 'Success',
        description: result.message || `Fixed musician relationships for "${selectedEvent.name}"`,
        variant: 'default'
      });
      
      console.log(`🔧 [Frontend] Refreshing data after successful fix`);
      // Refresh data after successful fix
      fetchData();
    } catch (err: unknown) {
      console.error('🔧 [Frontend] Error fixing event-talent relationship', err);
      
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : `Failed to fix event-talent relationship for ${selectedEvent.name}`,
        variant: 'destructive'
      });
    } finally {
      // Clear the fixing state for this event
      if (selectedEvent) {
        setFixingEvents(prev => ({ ...prev, [selectedEvent.id]: false }));
      }
      // Clear the selected event
      setSelectedEvent(null);
      console.log(`🔧 [Frontend] Fix operation completed`);
    }
  };

  // Determine status message and icon color
  const getStatusDetails = () => {
    if (!counts) return { message: 'Loading...', status: 'neutral' };
    
    // Check if there are any actual problematic relationships in the dropdown lists
    const hasProblematicRelations = missingEvents.length > 0 || mismatchedEvents.length > 0;
    
    // If there are no problematic relations to troubleshoot, show green
    // Only show yellow warning when there's something that needs attention
    if (!hasProblematicRelations) {
      return { 
        message: counts.missingTalents === 0 
          ? 'All events have talent relationships' 
          : `${counts.missingTalents} events with no musician relations`, 
        status: 'good' 
      };
    }
    
    // Show yellow warning when there are items in the dropdown lists that need attention
    return {
      message: `${counts.missingTalents} events (2025+) with no musician relations`,
      status: 'warning'
    };
  };
  
  // Get the appropriate icon color based on status
  const getIconColorClass = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'bad': return 'text-red-500';
      default: return 'text-muted-foreground';
    }
  };
  
  const statusDetails = getStatusDetails();
  
  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return 'No date';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center space-y-0 pb-2">
        <GitMerge className={`h-5 w-5 mr-2 ${getIconColorClass(statusDetails.status)}`} />
        <div>
          <CardTitle className="text-xl">Events - Musicians</CardTitle>
        </div>
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="mt-2 text-sm text-muted-foreground">Loading relationship data...</p>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-8 px-4">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <p className="text-sm text-red-500">{error}</p>
          </div>
        ) : (
          <>
            <div className="flex flex-col space-y-4">
              {/* Status indicator */}
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <p className="text-sm">{statusDetails.message}</p>
              </div>

              {/* CSV Fix explanation */}
              <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-md p-3">
                <div className="flex items-start space-x-2">
                  <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-blue-900 dark:text-blue-100">Recommended: Use CSV Fix</p>
                    <p className="text-blue-700 dark:text-blue-300 mt-1">
                      The CSV contains 16,274 verified talent-event relationships from MSSQL source of truth.
                      This is the fastest and most accurate way to sync all relationships.
                    </p>
                    <p className="text-blue-600 dark:text-blue-400 mt-1 text-xs">
                      ✓ Validates against MSSQL source • ✓ Efficient batch processing • ✓ Handles duplicates
                    </p>
                  </div>
                </div>
              </div>
              
              {/* Action buttons */}
              <div className="flex space-x-2">
                {/* Fix All button commented out due to performance concerns with 14,000+ events
                <Button 
                  onClick={handleFixAll} 
                  disabled={isBulkFixing || counts?.missingTalents === 0}
                  className="flex items-center"
                >
                  {isBulkFixing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Fixing...
                    </>
                  ) : (
                    <>
                      <Zap className="mr-2 h-4 w-4" />
                      Fix All Relationships
                    </>
                  )}
                </Button>
                */}
                
                {/* Batch Fix button - fixes up to 10 events at a time */}
                <Button 
                  onClick={handleBatchFix} 
                  disabled={batchFixing || missingEvents.length === 0}
                  className="flex items-center"
                  variant="outline"
                >
                  {batchFixing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Fixing {batchFixProgress.current}/{batchFixProgress.total}...
                    </>
                  ) : (
                    <>
                      <Wrench className="mr-2 h-4 w-4" />
                      Batch Fix (10 Events)
                    </>
                  )}
                </Button>
                
                {/* CSV Fix button - processes all relationships from CSV */}
                <Button
                  onClick={handleCsvFix}
                  disabled={csvFixing}
                  className="flex items-center"
                  variant="default"
                >
                  {csvFixing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing CSV...
                    </>
                  ) : (
                    <>
                      <FileText className="mr-2 h-4 w-4" />
                      Fix from CSV (16,274 relationships)
                    </>
                  )}
                </Button>
                
                {/* Refresh button */}
                <Button
                  variant="outline"
                  size="icon"
                  onClick={fetchData}
                  disabled={isLoading || batchFixing || csvFixing}
                  title="Refresh data"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Batch fix progress indicator */}
              {batchFixing && (
                <div className="mt-2 p-2 bg-muted rounded-md">
                  <div className="text-sm mb-1 flex justify-between">
                    <span>Batch Fix Progress</span>
                    <span>{batchFixProgress.current}/{batchFixProgress.total} events</span>
                  </div>
                  <div className="w-full bg-secondary h-2 rounded-full overflow-hidden">
                    <div 
                      className="bg-primary h-full transition-all duration-300 ease-in-out" 
                      style={{ width: `${(batchFixProgress.current / Math.max(batchFixProgress.total, 1)) * 100}%` }}
                    />
                  </div>
                  <div className="flex justify-between text-xs mt-1">
                    <span className="text-green-500">{batchFixProgress.success} successful</span>
                    <span className="text-red-500">{batchFixProgress.failed} failed</span>
                  </div>
                </div>
              )}
              
              {/* Event sections */}
              <div className="space-y-4">
                {/* Missing Talents section */}
                <Collapsible 
                  open={expanded.missing} 
                  onOpenChange={() => toggleExpanded('missing')}
                  className="border rounded-md"
                >
                  <CollapsibleTrigger className="flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors">
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4" />
                      <span>Events Missing Musician Relationships ({missingEvents.length})</span>
                    </div>
                    {expanded.missing ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent className="px-4 pb-4">
                    {missingEvents.length === 0 ? (
                      <p className="text-sm text-muted-foreground py-2">No events missing musician relationships</p>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Event Name</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Venue</TableHead>
                            <TableHead>Azure Talent IDs</TableHead>
                            <TableHead>PostgreSQL Talents</TableHead>
                            <TableHead>Action</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {missingEvents.map(event => (
                            <TableRow key={event.id}>
                              <TableCell className="font-medium">{event.name}</TableCell>
                              <TableCell>{formatDate(event.date)}</TableCell>
                              <TableCell>{event.venue_name}</TableCell>
                              <TableCell>{event.azure_talent_ids.length}</TableCell>
                              <TableCell className="text-red-500">{event.postgres_talent_count}</TableCell>
                              <TableCell>
                                <Button
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => openFixConfirmDialog(event)}
                                  disabled={fixingEvents[event.id]}
                                >
                                  {fixingEvents[event.id] ? (
                                    <>
                                      <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                                      Fixing...
                                    </>
                                  ) : (
                                    "Fix"
                                  )}
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </CollapsibleContent>
                </Collapsible>
                
                {/* Mismatched Talents section */}
                <Collapsible 
                  open={expanded.mismatched} 
                  onOpenChange={() => toggleExpanded('mismatched')}
                  className="border rounded-md"
                >
                  <CollapsibleTrigger className="flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors">
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4" />
                      <span>Events with Mismatched Musician Counts ({mismatchedEvents.length})</span>
                    </div>
                    {expanded.mismatched ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent className="px-4 pb-4">
                    {mismatchedEvents.length === 0 ? (
                      <p className="text-sm text-muted-foreground py-2">No events with mismatched musician counts</p>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Event Name</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Venue</TableHead>
                            <TableHead>Azure Talent IDs</TableHead>
                            <TableHead>PostgreSQL Talents</TableHead>
                            <TableHead>Action</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {mismatchedEvents.map(event => (
                            <TableRow key={event.id}>
                              <TableCell className="font-medium">{event.name}</TableCell>
                              <TableCell>{formatDate(event.date)}</TableCell>
                              <TableCell>{event.venue_name}</TableCell>
                              <TableCell>{event.azure_talent_ids.length}</TableCell>
                              <TableCell className="text-yellow-500">{event.postgres_talent_count}</TableCell>
                              <TableCell>
                                <Button
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => openFixConfirmDialog(event)}
                                  disabled={fixingEvents[event.id]}
                                >
                                  {fixingEvents[event.id] ? (
                                    <>
                                      <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                                      Fixing...
                                    </>
                                  ) : (
                                    "Fix"
                                  )}
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </CollapsibleContent>
                </Collapsible>
              </div>
            </div>
          </>
        )}
      </CardContent>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Fix Event-Musician Relationship</DialogTitle>
            <DialogDescription>
              {selectedEvent && (
                <div className="space-y-4 py-2">
                  <p>Are you sure you want to fix the musician relationships for this event?</p>
                  
                  <div className="bg-muted p-3 rounded-md text-sm">
                    <p><strong>Event:</strong> {selectedEvent.name}</p>
                    <p><strong>Date:</strong> {formatDate(selectedEvent.date)}</p>
                    <p><strong>Venue:</strong> {selectedEvent.venue_name}</p>
                    <p><strong>Azure Talents:</strong> {selectedEvent.azure_talent_ids.length}</p>
                    <p><strong>PostgreSQL Talents:</strong> {selectedEvent.postgres_talent_count}</p>
                  </div>
                  
                  <p>This will:</p>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>Retrieve talent relationships from Azure SQL</li>
                    <li>Update the PostgreSQL database to match</li>
                    <li>Add missing talent relationships</li>
                    <li>Remove incorrect talent relationships</li>
                  </ul>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleFixSingleEvent}>Fix</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
