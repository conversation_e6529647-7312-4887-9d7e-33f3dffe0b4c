"use client";

import { useEffect, useState } from "react";
import { Loader2, AlertCircle, Database, Calendar, Users, MapPin, Copy, ChevronDown, ChevronRight, MergeIcon, Check, Clock, Image } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { getApiUrl } from "@/lib/config";
import { useRouter } from "next/navigation";

// Import our component types
import { 
  EntityType, 
  ValidationIssue, 
  ValidationIssuesByType, 
  ValidationCounts,
  EntityRecord
} from "./types";

// Import our child components
import { StatsPanel } from "./stats-panel";
import { IssueList } from "./issue-list";
import { MergeDialog } from "./merge-dialog";
import { FixDialog } from "./fix-dialog";

export function IdValidation() {
  // State for validation data
  const [validationCounts, setValidationCounts] = useState<ValidationCounts | null>(null);
  const [issuesByType, setIssuesByType] = useState<ValidationIssuesByType>({});
  const [loadingCounts, setLoadingCounts] = useState<boolean>(true);
  const [loadingIssues, setLoadingIssues] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [activeEntityType, setActiveEntityType] = useState<EntityType | null>(null);
  
  // Dialogs state
  const [fixDialogOpen, setFixDialogOpen] = useState<boolean>(false);
  const [mergeDialogOpen, setMergeDialogOpen] = useState<boolean>(false);
  const [selectedIssue, setSelectedIssue] = useState<ValidationIssue | null>(null);
  const [isMerging, setIsMerging] = useState<boolean>(false);
  const [selectedMatchId, setSelectedMatchId] = useState<string>("");

  // Expanded state for collapsible sections
  const [expanded, setExpanded] = useState<{ 
    venues: boolean; 
    musicians: boolean; 
    events: boolean 
  }>({ 
    venues: false, 
    musicians: false, 
    events: false 
  });

  const [syncResults, setSyncResults] = useState<{ 
    eventsChecked: number, 
    eventsFixed: number, 
    eventsSkipped: number, 
    fixedEvents: string[],
    timestamp: string
  } | null>(null);
  const [imageFixResults, setImageFixResults] = useState<{
    total: number;
    processed: number;
    success: number;
    skipped: number;
    errors: number;
    timestamp: string;
  } | null>(null);
  const [isSyncing, setIsSyncing] = useState<boolean>(false);
  const [isFixingImages, setIsFixingImages] = useState<boolean>(false);
  const [isBatchFixing, setIsBatchFixing] = useState<boolean>(false);
  const [batchFixResults, setBatchFixResults] = useState<any>(null);

  const { getAccessToken } = useAuth();
  const { toast } = useToast();
  const router = useRouter();
  
  // Toggle expanded state for a specific section
  const toggleExpanded = (section: 'venues' | 'musicians' | 'events') => {
    setExpanded(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
    
    // If we're expanding a section and don't have the data yet, fetch it
    if (!expanded[section] && section === 'venues' && (!issuesByType.venues || issuesByType.venues.length === 0)) {
      fetchIssues('venues');
    } else if (!expanded[section] && section === 'musicians' && (!issuesByType.musicians || issuesByType.musicians.length === 0)) {
      fetchIssues('musicians');
    } else if (!expanded[section] && section === 'events' && (!issuesByType.events || issuesByType.events.length === 0)) {
      fetchIssues('events');
    }
  };

  // Fetch validation counts on load
  // Complete refresh function - exactly matching duplicate detection approach
  const refreshData = async () => {
    try {
      setLoadingCounts(true);
      setError(null);
      
      const apiUrl = getApiUrl();
      const token = await getAccessToken();
      
      // Fetch fresh validation counts
      const countsResponse = await fetch(`${apiUrl}/admin/id-validation/counts`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!countsResponse.ok) {
        throw new Error("Failed to fetch validation counts");
      }

      const data = await countsResponse.json();
      console.log('Fetched validation counts:', data);
      
      // Update counts
      setValidationCounts(data);
      
      // CRITICAL: Complete reset of all issues data
      // This is the key difference from our previous approach
      setIssuesByType({});
      
      // Fetch venue issues if count > 0 and section is expanded
      if (data.venueCount > 0 && (expanded.venues || activeEntityType === 'venues')) {
        console.log('Fetching venue validation issues');
        const timestamp = new Date().getTime(); // Add cache busting
        const venueResponse = await fetch(`${apiUrl}/admin/id-validation/issues/venues?t=${timestamp}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            "Cache-Control": "no-cache, no-store, must-revalidate", 
            "Pragma": "no-cache"
          },
        });
        
        if (venueResponse.ok) {
          const venueData = await venueResponse.json();
          console.log(`Received ${venueData.length} venue issues`); 
          setIssuesByType(prev => ({ ...prev, venues: venueData }));
        }
      }
      
      // Fetch musician issues if count > 0 and section is expanded
      if (data.musicianCount > 0 && (expanded.musicians || activeEntityType === 'musicians')) {
        console.log('Fetching musician validation issues');
        const timestamp = new Date().getTime(); // Add cache busting
        const musicianResponse = await fetch(`${apiUrl}/admin/id-validation/issues/musicians?t=${timestamp}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            "Cache-Control": "no-cache, no-store, must-revalidate", 
            "Pragma": "no-cache"
          },
        });
        
        if (musicianResponse.ok) {
          const musicianData = await musicianResponse.json();
          console.log(`Received ${musicianData.length} musician issues`);
          setIssuesByType(prev => ({ ...prev, musicians: musicianData }));
        }
      }
      
      // Fetch event issues if count > 0 and section is expanded
      if (data.eventCount > 0 && (expanded.events || activeEntityType === 'events')) {
        console.log('Fetching event validation issues');
        const timestamp = new Date().getTime(); // Add cache busting
        const eventResponse = await fetch(`${apiUrl}/admin/id-validation/issues/events?t=${timestamp}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            "Cache-Control": "no-cache, no-store, must-revalidate", 
            "Pragma": "no-cache"
          },
        });
        
        if (eventResponse.ok) {
          const eventData = await eventResponse.json();
          console.log(`Received ${eventData.length} event issues`);
          setIssuesByType(prev => ({ ...prev, events: eventData }));
        }
      }
      
      return data;
    } catch (err) {
      console.error("Error refreshing data:", err);
      setError("Failed to refresh validation data. Please try again.");
      toast({
        title: "Error",
        description: "Failed to refresh validation data. Please try again.",
        variant: "destructive",
      });
      return null;
    } finally {
      setLoadingCounts(false);
    }
  };
  
  useEffect(() => {
    refreshData();
  }, []);

  // Fetch issues when an entity type is selected
  useEffect(() => {
    if (activeEntityType) {
      fetchIssues(activeEntityType);
    }
  }, [activeEntityType]);

  // Fetch validation counts
  const fetchValidationCounts = async () => {
    try {
      setLoadingCounts(true);
      setError(null);
      
      const apiUrl = getApiUrl();
      const token = await getAccessToken();
      
      const response = await fetch(`${apiUrl}/admin/id-validation/counts`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch validation counts");
      }

      const data = await response.json();
      setValidationCounts(data);
      
      // Return the data for direct use
      return data;
    } catch (err) {
      console.error("Error fetching validation counts:", err);
      setError("Failed to fetch validation counts. Please try again.");
      toast({
        title: "Error",
        description: "Failed to fetch validation counts. Please try again.",
        variant: "destructive",
      });
      
      // Return null in case of error
      return null;
    } finally {
      setLoadingCounts(false);
    }
  };

  // Fetch issues for a specific entity type
  const fetchIssues = async (entityType: EntityType) => {
    try {
      setLoadingIssues(true);
      setError(null);
      
      const apiUrl = getApiUrl();
      const token = await getAccessToken();
      
      console.log(`Fetching ${entityType} issues`);
      
      const response = await fetch(`${apiUrl}/admin/id-validation/issues/${entityType}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch ${entityType} issues`);
      }

      const data = await response.json();
      console.log(`Received ${data.length} ${entityType} issues`);
      
      // Define the issue type to fix TypeScript lint errors
      interface ValidationIssue {
        id: string;
        entityType: string;
        issueType: string;
        postgresRecord: any;
        azureRecord: any;
        description: string;
        createdAt: string;
      }
      
      // Debug: Log the breakdown of issue types
      const missingAzure = data.filter((issue: ValidationIssue) => issue.issueType === 'missing_azure').length;
      const missingPostgres = data.filter((issue: ValidationIssue) => issue.issueType === 'missing_postgres').length;
      console.log(`Issue breakdown - missing_azure: ${missingAzure}, missing_postgres: ${missingPostgres}`);
      
      // Debug: Log a sample of each issue type if available
      if (missingAzure > 0) {
        console.log('Sample missing_azure issue:', data.find((issue: ValidationIssue) => issue.issueType === 'missing_azure'));
      }
      if (missingPostgres > 0) {
        console.log('Sample missing_postgres issue:', data.find((issue: ValidationIssue) => issue.issueType === 'missing_postgres'));
      }
      
      // Apply a complete replacement of data rather than merging
      setIssuesByType(prev => ({
        ...prev,
        [entityType]: data
      }));
      
      return data;
    } catch (err) {
      console.error(`Error fetching ${entityType} issues:`, err);
      setError(`Failed to fetch ${entityType} issues. Please try again.`);
      toast({
        title: "Error",
        description: `Failed to fetch ${entityType} issues. Please try again.`,
        variant: "destructive",
      });
      return [];
    } finally {
      setLoadingIssues(false);
    }
  };

  // Handle opening fix dialog for an issue
  const handleFixIssue = (issue: ValidationIssue) => {
    setSelectedIssue(issue);
    setFixDialogOpen(true);
  };

  // Handle opening merge dialog for an issue
  const handleMergeIssue = (issue: ValidationIssue) => {
    setSelectedIssue(issue);
    setMergeDialogOpen(true);
  };

  // Handle resolving an issue
  const handleResolveIssue = async (issueId: string, resolution: string, options?: string | { azureId?: string, mergeFields?: boolean }) => {
    try {
      setIsMerging(true);
      
      const apiUrl = getApiUrl();
      const token = await getAccessToken();
      
      const payload: any = { resolution };
      
      // Handle different types of the options parameter
      if (typeof options === 'string') {
        // It's a regular matchId
        payload.matchId = options;
      } else if (options && typeof options === 'object') {
        // It's an options object with possible azureId and mergeFields
        if ('azureId' in options) {
          payload.azureId = options.azureId;
        }
        
        if ('mergeFields' in options) {
          payload.mergeFields = options.mergeFields;
        }
      }
      
      // Close any open dialogs right away for better UX
      setFixDialogOpen(false);
      setMergeDialogOpen(false);
      
      // Now send the API request
      const response = await fetch(`${apiUrl}/admin/id-validation/resolve/${issueId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error("Failed to resolve issue");
      }

      toast({
        title: "Success",
        description: "Issue resolved successfully",
      });
      
      // Simple, direct approach - exactly like duplicate detection component
      const latestCounts = await refreshData();
      
      // Auto-collapse sections when they reach zero count
      if (latestCounts) {
        if (activeEntityType === 'venues' && latestCounts.venueCount === 0) {
          setExpanded(prev => ({ ...prev, venues: false }));
        } else if (activeEntityType === 'musicians' && latestCounts.musicianCount === 0) {
          setExpanded(prev => ({ ...prev, musicians: false }));
        } else if (activeEntityType === 'events' && latestCounts.eventCount === 0) {
          setExpanded(prev => ({ ...prev, events: false }));
        }
      }
      
    } catch (err) {
      console.error("Error resolving issue:", err);
      toast({
        title: "Error",
        description: "Failed to resolve issue. Please try again.",
        variant: "destructive",
      });
      
      // If there was an error, do a complete refresh to ensure accurate state
      await refreshData();
    } finally {
      setIsMerging(false);
    }
  };
  

  // Handle merge for duplicate records
  const handleMerge = async (primaryId: string, duplicateIds: string[]) => {
    try {
      setIsMerging(true);
      
      const apiUrl = getApiUrl();
      const token = await getAccessToken();
      
      const response = await fetch(`${apiUrl}/admin/id-validation/merge`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          primaryId,
          duplicateIds
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to merge records");
      }

      toast({
        title: "Success",
        description: "Records merged successfully",
      });

      // Refresh the issues and counts
      if (activeEntityType) {
        fetchIssues(activeEntityType);
      }
      fetchValidationCounts();
      
      // Close any open dialogs
      setMergeDialogOpen(false);
      return true;
    } catch (error) {
      console.error("Error merging records:", error);
      toast({
        title: "Error",
        description: "Failed to merge records. Please try again.",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsMerging(false);
    }
  };

  // Open merge dialog when duplicate group is selected
  const showMergeDialog = (ids: string[], metadata?: any[]) => {
    // Only open the merge dialog if there's a selected issue
    if (selectedIssue) {
      setMergeDialogOpen(true);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {/* Icon color is conditional based on whether there are any validation issues */}
          <Database className={`h-5 w-5 ${(!validationCounts || 
            (parseInt(validationCounts.venueCount as any) === 0 && 
             parseInt(validationCounts.musicianCount as any) === 0 && 
             parseInt(validationCounts.eventCount as any) === 0)) 
               ? 'text-green-500' : 'text-yellow-500'}`} />
          ID Validation
        </CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="bg-destructive/10 border border-destructive rounded-md p-4 mb-6">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <p className="font-medium text-destructive">{error}. Please try again.</p>
            </div>
          </div>
        )}

        {loadingCounts && !validationCounts ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p>Loading ID validation data...</p>
            </div>
          </div>
        ) : (
        <>
              <div className="space-y-6">
                {/* <div className="mb-4"> */}
                  {/* <p className="text-sm text-muted-foreground">
                    This tool helps you identify and fix ID inconsistencies between PostgreSQL and Azure SQL databases.
                    Select a category to view detailed issues.
                  </p> */}
                {/* </div> */}

                {/* <div className="flex justify-end mb-4"> */}
                  {/* <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchValidationCounts}
                    disabled={loadingCounts}
                  >
                    {loadingCounts ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Refreshing...
                      </>
                    ) : (
                      <>
                        <Database className="mr-2 h-4 w-4" />
                        Refresh Data
                      </>
                    )}
                  </Button> */}
                {/* </div> */}
                
                {/* Stats Overview */}
                {/* <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-secondary/20 p-4 rounded-md text-center">
                    <h3 className="text-lg font-semibold flex items-center justify-center gap-2">
                      <MapPin className="h-4 w-4" /> Venues
                    </h3>
                    <p className="text-2xl font-bold mt-2">
                      {loadingCounts ? (
                        <Loader2 className="h-5 w-5 animate-spin mx-auto" />
                      ) : (
                        validationCounts?.venueCount || 0
                      )}
                    </p>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="mt-2"
                      onClick={() => toggleExpanded('venues')}
                      disabled={loadingCounts || (validationCounts?.venueCount || 0) === 0}
                    >
                      View Issues
                    </Button>
                  </div>
                  
                  <div className="bg-secondary/20 p-4 rounded-md text-center">
                    <h3 className="text-lg font-semibold flex items-center justify-center gap-2">
                      <Users className="h-4 w-4" /> Musicians
                    </h3>
                    <p className="text-2xl font-bold mt-2">
                      {loadingCounts ? (
                        <Loader2 className="h-5 w-5 animate-spin mx-auto" />
                      ) : (
                        validationCounts?.musicianCount || 0
                      )}
                    </p>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="mt-2"
                      onClick={() => toggleExpanded('musicians')}
                      disabled={loadingCounts || (validationCounts?.musicianCount || 0) === 0}
                    >
                      View Issues
                    </Button>
                  </div>
                  
                  <div className="bg-secondary/20 p-4 rounded-md text-center">
                    <h3 className="text-lg font-semibold flex items-center justify-center gap-2">
                      <Calendar className="h-4 w-4" /> Events
                    </h3>
                    <p className="text-2xl font-bold mt-2">
                      {loadingCounts ? (
                        <Loader2 className="h-5 w-5 animate-spin mx-auto" />
                      ) : (
                        validationCounts?.eventCount || 0
                      )}
                    </p>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="mt-2"
                      onClick={() => toggleExpanded('events')}
                      disabled={loadingCounts || (validationCounts?.eventCount || 0) === 0}
                    >
                      View Issues
                    </Button>
                  </div>
                </div> */}
                
                {/* Collapsible sections */}
                <div className="space-y-4">
                  {/* Venues section */}
                  <Collapsible 
                    open={expanded.venues} 
                    onOpenChange={() => toggleExpanded('venues')}
                    className="border rounded-md"
                  >
                    <CollapsibleTrigger className="flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors">
                      <div className="flex items-center">
                        <MapPin className="mr-2 h-4 w-4" />
                        <span>Venue ID Validation Issues ({validationCounts?.venueCount || 0})</span>
                      </div>
                      {expanded.venues ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </CollapsibleTrigger>
                    
                    <CollapsibleContent className="px-4 pb-4">
                      {loadingIssues ? (
                        <div className="py-4 text-center">
                          <Loader2 className="h-5 w-5 animate-spin mx-auto mb-2" />
                          <p className="text-sm text-muted-foreground">Loading venue issues...</p>
                        </div>
                      ) : issuesByType.venues && issuesByType.venues.length > 0 ? (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Name</TableHead>
                              <TableHead>PostgreSQL ID</TableHead>
                              <TableHead>Azure ID</TableHead>
                              <TableHead>Issue Type</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {issuesByType.venues.map((issue) => (
                              <TableRow key={issue.id}>
                                <TableCell>
                                  {issue.postgresRecord?.name || issue.azureRecord?.name || 'Unknown'}
                                </TableCell>
                                <TableCell>
                                  {issue.postgresRecord?.id ? (
                                    <span className="font-mono text-xs">{issue.postgresRecord.id.substring(0, 8)}...</span>
                                  ) : (
                                    <span className="text-muted-foreground text-xs italic">Missing</span>
                                  )}
                                </TableCell>
                                <TableCell>
                                  {issue.azureRecord?.id ? (
                                    <span className="font-mono text-xs">{issue.azureRecord.id.substring(0, 8)}...</span>
                                  ) : (
                                    <span className="text-muted-foreground text-xs italic">Missing</span>
                                  )}
                                </TableCell>
                                <TableCell>
                                  <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {issue.issueType === 'missing_azure' ? 'missing azure' : issue.issueType.replace('_', ' ')}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div className="flex space-x-2">
                                    <Button 
                                      variant="outline" 
                                      size="sm"
                                      onClick={() => handleFixIssue(issue)}
                                    >
                                      Fix
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      ) : (
                        <p className="text-sm text-muted-foreground py-2">No venue ID validation issues found</p>
                      )}
                    </CollapsibleContent>
                  </Collapsible>
                  
                  {/* Musicians section - for now just show a placeholder */}
                  <Collapsible 
                    open={expanded.musicians} 
                    onOpenChange={() => toggleExpanded('musicians')}
                    className="border rounded-md"
                  >
                    <CollapsibleTrigger className="flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors">
                      <div className="flex items-center">
                        <Users className="mr-2 h-4 w-4" />
                        <span>Musician ID Validation Issues ({validationCounts?.musicianCount || 0})</span>
                      </div>
                      {expanded.musicians ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </CollapsibleTrigger>
                    
                    <CollapsibleContent className="px-4 pb-4">
                      {loadingIssues ? (
                        <div className="py-4 text-center">
                          <Loader2 className="h-5 w-5 animate-spin mx-auto mb-2" />
                          <p className="text-sm text-muted-foreground">Loading musician issues...</p>
                        </div>
                      ) : (
                        issuesByType.musicians && issuesByType.musicians.length > 0 ? (
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>PostgreSQL ID</TableHead>
                                <TableHead>Issue</TableHead>
                                <TableHead>Created</TableHead>
                                <TableHead>Actions</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {issuesByType.musicians.map((issue) => (
                                <TableRow key={issue.id}>
                                  <TableCell className="font-medium">{issue.postgresRecord?.name || 'Unknown'}</TableCell>
                                  <TableCell>
                                    <code className="bg-secondary/50 px-1 py-0.5 rounded text-xs">
                                      {issue.id}
                                    </code>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6 ml-1"
                                      onClick={() => {
                                        navigator.clipboard.writeText(issue.id);
                                        toast({
                                          title: "Copied to clipboard",
                                          description: "The musician ID has been copied to your clipboard.",
                                        });
                                      }}
                                    >
                                      <Copy className="h-3 w-3" />
                                    </Button>
                                  </TableCell>
                                  <TableCell>{issue.issueType}</TableCell>
                                  <TableCell>
                                    {issue.postgresRecord?.createdAt 
                                      ? new Date(issue.postgresRecord.createdAt).toLocaleDateString() 
                                      : 'Unknown'}
                                  </TableCell>
                                  <TableCell>
                                    <div className="flex space-x-2">
                                      <Button 
                                        variant="outline" 
                                        size="sm"
                                        onClick={() => handleFixIssue(issue)}
                                      >
                                        Fix
                                      </Button>
                                    </div>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        ) : (
                          <p className="text-sm text-muted-foreground py-2">No musician ID validation issues found</p>
                        )
                      )}
                    </CollapsibleContent>
                  </Collapsible>
                  
                  {/* Events section - for now just show a placeholder */}
                  <Collapsible 
                    open={expanded.events} 
                    onOpenChange={() => toggleExpanded('events')}
                    className="border rounded-md"
                  >
                    <CollapsibleTrigger className="flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors">
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4" />
                        <span>Event ID Validation Issues ({validationCounts?.eventCount || 0})</span>
                      </div>
                      {expanded.events ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </CollapsibleTrigger>
                    
                    <CollapsibleContent className="px-4 pb-4">
                      {/* Action buttons - always visible when expanded */}
                      <div className="mb-4 flex flex-col sm:flex-row gap-2 justify-center">
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="flex items-center"
                          disabled={isSyncing || isFixingImages}
                          onClick={async () => {
                            try {
                              setIsFixingImages(true);
                              const token = await getAccessToken();
                              const response = await fetch(`${getApiUrl()}/admin/events/sync/fix-event-images`, {
                                method: 'POST',
                                headers: {
                                  'Content-Type': 'application/json',
                                  'Authorization': `Bearer ${token}`
                                },
                              });
                              
                              if (!response.ok) {
                                throw new Error('Failed to fix event images');
                              }
                              
                              const result = await response.json();

                              // Check if this is the new message response
                              if (result.message) {
                                toast({
                                  title: "Manual Action Required",
                                  description: result.message,
                                  variant: "default"
                                });
                                return;
                              }

                              // Store the image fix results for display
                              setImageFixResults({
                                total: result.total || 0,
                                processed: result.processed || 0,
                                success: result.success || 0,
                                skipped: result.skipped || 0,
                                errors: result.errors || 0,
                                timestamp: new Date().toLocaleString()
                              });

                              toast({
                                title: "Image Fix Complete",
                                description: `Fixed ${result.success} event images out of ${result.total} events checked.`,
                              });
                              
                              // Refresh the validation issues after fixing images
                              refreshData();
                            } catch (error) {
                              console.error('Error fixing event images:', error);
                              toast({
                                title: "Image Fix Failed",
                                description: "Failed to fix event images. See console for details.",
                                variant: "destructive"
                              });
                            } finally {
                              setIsFixingImages(false);
                              setLoadingIssues(false);
                            }
                          }}
                        >
                          {isFixingImages ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            <>
                              <Image className="mr-2 h-4 w-4" />
                              Fix Event Images
                            </>
                          )}
                        </Button>

                        <Button 
                          variant="outline" 
                          size="sm"
                          className="flex items-center"
                          disabled={isSyncing || isFixingImages}
                          onClick={async () => {
                            try {
                              setIsSyncing(true);
                              const token = await getAccessToken();
                              const response = await fetch(`${getApiUrl()}/admin/events/sync/fix-event-datetimes`, {
                                method: 'POST',
                                headers: {
                                  'Authorization': `Bearer ${token}`,
                                  'Content-Type': 'application/json',
                                },
                              });
                              
                              if (!response.ok) {
                                throw new Error('Failed to sync event date/times');
                              }
                              
                              const result = await response.json();
                              
                              // Store the sync results for display
                              setSyncResults({
                                eventsChecked: result.eventsFound || 0,
                                eventsFixed: result.eventsImported || 0,
                                eventsSkipped: result.eventsSkipped || 0,
                                fixedEvents: result.details?.updatedEvents || [],
                                timestamp: new Date().toLocaleString()
                              });
                              
                              toast({
                                title: "Date/Time Sync Complete",
                                description: `Fixed ${result.eventsImported} event datetime issues out of ${result.eventsFound} events checked.`,
                              });
                              
                              // Refresh the validation issues after syncing
                              refreshData();
                            } catch (error) {
                              console.error('Error syncing event date/times:', error);
                              toast({
                                title: "Sync Failed",
                                description: "Failed to sync event date/times. See console for details.",
                                variant: "destructive"
                              });
                            } finally {
                              setIsSyncing(false);
                              setLoadingIssues(false);
                            }
                          }}
                        >
                          {isSyncing ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Syncing...
                            </>
                          ) : (
                            <>
                              <RefreshCw className="mr-2 h-4 w-4" />
                              Sync Date/Times
                            </>
                          )}
                        </Button>
                      </div>
                      
                      {loadingIssues ? (
                        <div className="py-4 text-center">
                          <Loader2 className="h-5 w-5 animate-spin mx-auto mb-2" />
                          <p className="text-sm text-muted-foreground">Loading event issues...</p>
                        </div>
                      ) : issuesByType.events && issuesByType.events.length > 0 ? (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Name</TableHead>
                              <TableHead>Venue</TableHead>
                              <TableHead>Start Date</TableHead>
                              <TableHead>PostgreSQL ID</TableHead>
                              <TableHead>Issue</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {/* Batch Fix button at the top of the table */}
                            <TableRow>
                              <TableCell colSpan={6} className="text-center">
                                {/* Add Batch Fix button */}
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  className="flex items-center mx-auto mt-2"
                                  disabled={isBatchFixing || issuesByType.events?.filter(issue => issue.issueType === 'missing_postgres').length === 0}
                                  onClick={async () => {
                                    try {
                                      setIsBatchFixing(true);
                                      const token = await getAccessToken();
                                      const response = await fetch(`${getApiUrl()}/admin/id-validation/batch-fix-events`, {
                                        method: 'POST',
                                        headers: {
                                          'Content-Type': 'application/json',
                                          'Authorization': `Bearer ${token}`
                                        },
                                      });
                                      
                                      if (!response.ok) {
                                        throw new Error('Failed to batch fix missing events');
                                      }
                                      
                                      const result = await response.json();
                                      
                                      // Store the batch fix results for display
                                      setBatchFixResults({
                                        totalIssues: result.totalIssues || 0,
                                        successCount: result.successCount || 0,
                                        errorCount: result.errorCount || 0,
                                        fixedEvents: result.fixedEvents || [],
                                        errors: result.errors || [],
                                        timestamp: new Date().toLocaleString()
                                      });
                                      
                                      toast({
                                        title: "Batch Fix Complete",
                                        description: `Fixed ${result.successCount} missing events out of ${result.totalIssues} issues.`,
                                      });
                                      
                                      // Refresh the validation issues after batch fixing
                                      refreshData();
                                    } catch (error) {
                                      console.error('Error batch fixing events:', error);
                                      toast({
                                        title: "Batch Fix Failed",
                                        description: "Failed to batch fix missing events. See console for details.",
                                        variant: "destructive"
                                      });
                                    } finally {
                                      setIsBatchFixing(false);
                                      setLoadingIssues(false);
                                    }
                                  }}
                                >
                                  {isBatchFixing ? (
                                    <>
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                      Fixing...
                                    </>
                                  ) : (
                                    <>
                                      <MergeIcon className="mr-2 h-4 w-4" />
                                      Batch Fix Missing Events
                                    </>
                                  )}
                                </Button>
                                

                                
                                {/* Results display area */}
                                {syncResults && (
                                  <div className="mt-4 p-3 bg-muted/50 rounded-md text-sm">
                                    <div className="flex items-center justify-between mb-2">
                                      <h4 className="font-medium flex items-center">
                                        <Clock className="h-4 w-4 mr-1" /> 
                                        Sync Results
                                      </h4>
                                      <span className="text-xs text-muted-foreground">
                                        {syncResults.timestamp}
                                      </span>
                                    </div>
                                    
                                    <div className="grid grid-cols-3 gap-2 mb-3">
                                      <div className="bg-background p-2 rounded">
                                        <div className="text-xs text-muted-foreground">Checked</div>
                                        <div className="font-medium">{syncResults.eventsChecked}</div>
                                      </div>
                                      <div className="bg-background p-2 rounded">
                                        <div className="text-xs text-muted-foreground">Fixed</div>
                                        <div className="font-medium text-green-600">{syncResults.eventsFixed}</div>
                                      </div>
                                      <div className="bg-background p-2 rounded">
                                        <div className="text-xs text-muted-foreground">Skipped</div>
                                        <div className="font-medium">{syncResults.eventsSkipped}</div>
                                      </div>
                                    </div>
                                    
                                    {syncResults.fixedEvents.length > 0 && (
                                      <>
                                        <div className="text-xs font-medium mb-1">Fixed Events:</div>
                                        <ScrollArea className="h-[100px] w-full rounded border p-2 bg-background">
                                          <ul className="text-xs space-y-1">
                                            {syncResults.fixedEvents.map((event: string, index: number) => (
                                              <li key={index} className="text-muted-foreground">
                                                • {event}
                                              </li>
                                            ))}
                                          </ul>
                                        </ScrollArea>
                                      </>
                                    )}
                                  </div>
                                )}
                                
                                {/* Batch Fix Results display area */}
                                {batchFixResults && (
                                  <div className="mt-4 p-3 bg-muted/50 rounded-md text-sm">
                                    <div className="flex items-center justify-between mb-2">
                                      <h4 className="font-medium flex items-center">
                                        <MergeIcon className="h-4 w-4 mr-1" /> 
                                        Batch Fix Results
                                      </h4>
                                      <span className="text-xs text-muted-foreground">
                                        {batchFixResults.timestamp}
                                      </span>
                                    </div>
                                    
                                    <div className="grid grid-cols-3 gap-2 mb-3">
                                      <div className="bg-background p-2 rounded">
                                        <div className="text-xs text-muted-foreground">Total Issues</div>
                                        <div className="font-medium">{batchFixResults.totalIssues}</div>
                                      </div>
                                      <div className="bg-background p-2 rounded">
                                        <div className="text-xs text-muted-foreground">Fixed</div>
                                        <div className="font-medium text-green-600">{batchFixResults.successCount}</div>
                                      </div>
                                      <div className="bg-background p-2 rounded">
                                        <div className="text-xs text-muted-foreground">Errors</div>
                                        <div className="font-medium text-red-600">{batchFixResults.errorCount}</div>
                                      </div>
                                    </div>
                                    
                                    {batchFixResults.fixedEvents.length > 0 && (
                                      <>
                                        <div className="text-xs font-medium mb-1">Fixed Events:</div>
                                        <ScrollArea className="h-[100px] w-full rounded border p-2 bg-background">
                                          <ul className="text-xs space-y-1">
                                            {batchFixResults.fixedEvents.map((event: any, index: number) => (
                                              <li key={index} className="text-muted-foreground">
                                                • {event.name} (ID: {event.id})
                                              </li>
                                            ))}
                                          </ul>
                                        </ScrollArea>
                                      </>
                                    )}
                                    
                                    {batchFixResults.errors.length > 0 && (
                                      <>
                                        <div className="text-xs font-medium mb-1 mt-3 text-red-600">Errors:</div>
                                        <ScrollArea className="h-[100px] w-full rounded border p-2 bg-background">
                                          <ul className="text-xs space-y-1">
                                            {batchFixResults.errors.map((error: any, index: number) => (
                                              <li key={index} className="text-red-500">
                                                • {error.event}: {error.error}
                                              </li>
                                            ))}
                                          </ul>
                                        </ScrollArea>
                                      </>
                                    )}
                                  </div>
                                )}
                              </TableCell>
                            </TableRow>
                            {issuesByType.events.map((issue) => (
                              <TableRow key={issue.id}>
                                <TableCell className="font-medium">{issue.postgresRecord?.name || issue.azureRecord?.name || 'Unknown Event'}</TableCell>
                                <TableCell>{issue.postgresRecord?.venue_name || issue.azureRecord?.venue_name || 'Unknown Venue'}</TableCell>
                                <TableCell>
                                  {(() => {
                                    const dateTime = issue.postgresRecord?.startDateTime || issue.azureRecord?.startDateTime;
                                    if (!dateTime) return 'Unknown';
                                    
                                    // MSSQL datetime2 format: "2025-10-05 00:00:00.0000000"
                                    // Extract just the date portion (YYYY-MM-DD) without any timezone conversion
                                    if (dateTime.includes(' ')) {
                                      return dateTime.split(' ')[0];
                                    }
                                    // Handle ISO format (2025-12-12T00:00:00.000Z) - extract date only
                                    if (dateTime.includes('T')) {
                                      return dateTime.split('T')[0];
                                    }
                                    // Already just a date
                                    return dateTime;
                                  })()}
                                </TableCell>
                                <TableCell>
                                  {issue.postgresRecord?.id ? (
                                    <>
                                      <code className="bg-secondary/50 px-1 py-0.5 rounded text-xs">
                                        {issue.postgresRecord.id.substring(0, 8)}...
                                      </code>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-6 w-6 ml-1"
                                        onClick={() => {
                                          navigator.clipboard.writeText(issue.postgresRecord.id);
                                          toast({
                                            title: "Copied to clipboard",
                                            description: "The PostgreSQL ID has been copied to your clipboard.",
                                          });
                                        }}
                                      >
                                        <Copy className="h-3 w-3" />
                                      </Button>
                                    </>
                                  ) : (
                                    <span className="text-muted-foreground text-xs italic">Missing</span>
                                  )}
                                </TableCell>
                                <TableCell>
                                  <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {issue.issueType === 'missing_azure' ? 'missing azure' : issue.issueType.replace('_', ' ')}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div className="flex space-x-2">
                                    <Button 
                                      variant="outline" 
                                      size="sm"
                                      onClick={() => handleFixIssue(issue)}
                                    >
                                      Fix
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      ) : (
                        <div className="py-2">
                          <p className="text-sm text-muted-foreground mb-4">No event ID validation issues found</p>
                          <div className="flex space-x-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              className="flex items-center mx-auto"
                              disabled={isSyncing || isFixingImages}
                              onClick={async () => {
                                try {
                                  setIsSyncing(true);
                                  const token = await getAccessToken();
                                  const response = await fetch(`${getApiUrl()}/admin/events/sync/fix-event-datetimes`, {
                                    method: 'POST',
                                    headers: {
                                      'Content-Type': 'application/json',
                                      'Authorization': `Bearer ${token}`
                                    },
                                  });
                                  
                                  if (!response.ok) {
                                    throw new Error('Failed to sync event date/times');
                                  }
                                  
                                  const result = await response.json();
                                  
                                  // Store the sync results for display
                                  setSyncResults({
                                    eventsChecked: result.eventsFound || 0,
                                    eventsFixed: result.eventsImported || 0,
                                    eventsSkipped: result.eventsSkipped || 0,
                                    fixedEvents: result.details?.updatedEvents || [],
                                    timestamp: new Date().toLocaleString()
                                  });
                                  
                                  toast({
                                    title: "Date/Time Sync Complete",
                                    description: `Fixed ${result.eventsImported} event datetime issues out of ${result.eventsFound} events checked.`,
                                  });
                                  
                                  // Refresh the validation issues after syncing
                                  refreshData();
                                } catch (error) {
                                  console.error('Error syncing event date/times:', error);
                                  toast({
                                    title: "Sync Failed",
                                    description: "Failed to sync event date/times. See console for details.",
                                    variant: "destructive"
                                  });
                                } finally {
                                  setIsSyncing(false);
                                  setLoadingIssues(false);
                                }
                              }}
                            >
                              {isSyncing ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Syncing...
                                </>
                              ) : (
                                <>
                                  <RefreshCw className="mr-2 h-4 w-4" />
                                  Sync Date/Times
                                </>
                              )}
                            </Button>
                          </div>
                          
                          {/* Date/Time Sync Results display area */}
                          {syncResults && (
                            <div className="mt-4 p-3 bg-muted/50 rounded-md text-sm">
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="font-medium flex items-center">
                                  <Clock className="h-4 w-4 mr-1" /> 
                                  Date/Time Sync Results
                                </h4>
                                <span className="text-xs text-muted-foreground">
                                  {syncResults.timestamp}
                                </span>
                              </div>
                              
                              <div className="grid grid-cols-3 gap-2 mb-3">
                                <div className="bg-background p-2 rounded">
                                  <div className="text-xs text-muted-foreground">Checked</div>
                                  <div className="font-medium">{syncResults.eventsChecked}</div>
                                </div>
                                <div className="bg-background p-2 rounded">
                                  <div className="text-xs text-muted-foreground">Fixed</div>
                                  <div className="font-medium text-green-600">{syncResults.eventsFixed}</div>
                                </div>
                                <div className="bg-background p-2 rounded">
                                  <div className="text-xs text-muted-foreground">Skipped</div>
                                  <div className="font-medium">{syncResults.eventsSkipped}</div>
                                </div>
                              </div>
                              
                              {syncResults.fixedEvents.length > 0 && (
                                <>
                                  <div className="text-xs font-medium mb-1">Fixed Events:</div>
                                  <ScrollArea className="h-[100px] w-full rounded border p-2 bg-background">
                                    <ul className="text-xs space-y-1">
                                      {syncResults.fixedEvents.map((event, index) => (
                                        <li key={index} className="text-muted-foreground">
                                          • {event}
                                        </li>
                                      ))}
                                    </ul>
                                  </ScrollArea>
                                </>
                              )}
                            </div>
                          )}
                          
                          {/* Image Fix Results display area */}
                          {imageFixResults && (
                            <div className="mt-4 p-3 bg-muted/50 rounded-md text-sm">
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="font-medium flex items-center">
                                  <Image className="h-4 w-4 mr-1" /> 
                                  Image Fix Results
                                </h4>
                                <span className="text-xs text-muted-foreground">
                                  {imageFixResults.timestamp}
                                </span>
                              </div>
                              
                              <div className="grid grid-cols-4 gap-2 mb-3">
                                <div className="bg-background p-2 rounded">
                                  <div className="text-xs text-muted-foreground">Total</div>
                                  <div className="font-medium">{imageFixResults.total}</div>
                                </div>
                                <div className="bg-background p-2 rounded">
                                  <div className="text-xs text-muted-foreground">Processed</div>
                                  <div className="font-medium">{imageFixResults.processed}</div>
                                </div>
                                <div className="bg-background p-2 rounded">
                                  <div className="text-xs text-muted-foreground">Success</div>
                                  <div className="font-medium text-green-600">{imageFixResults.success}</div>
                                </div>
                                <div className="bg-background p-2 rounded">
                                  <div className="text-xs text-muted-foreground">Errors</div>
                                  <div className="font-medium text-red-600">{imageFixResults.errors}</div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </CollapsibleContent>
                  </Collapsible>
                </div>
              </div>
          
          {selectedIssue && (
            <>
              <FixDialog
                open={fixDialogOpen}
                setOpen={setFixDialogOpen}
                issue={selectedIssue}
                onFix={async (issue, matchId) => {
                  // Check if the issue has the alreadyHandled flag
                  if (issue && 'alreadyHandled' in issue && issue.alreadyHandled === true) {
                    // Just refresh the data without calling handleResolveIssue
                    await refreshData();
                    return;
                  }
                  
                  // If no matchId is provided and no azureId in the issue, just refresh
                  if (!matchId && (!issue || !('azureId' in issue))) {
                    await refreshData();
                    return;
                  }
                  
                  // Otherwise, proceed with the normal flow
                  await handleResolveIssue(
                    issue?.id || selectedIssue?.id || '', 
                    'fix', 
                    'azureId' in issue ? { azureId: issue.azureId as string } : matchId || selectedMatchId
                  );
                }}
                matchId={selectedMatchId}
                setMatchId={setSelectedMatchId}
              />
              
              <MergeDialog
                open={mergeDialogOpen}
                setOpen={setMergeDialogOpen}
                type={selectedIssue.entityType}
                duplicateGroup={{
                  ids: [selectedIssue.id],
                  recordMetadata: [{
                    id: selectedIssue.id,
                    name: selectedIssue.postgresRecord?.name || selectedIssue.azureRecord?.name || 'Unknown',
                    createdAt: selectedIssue.postgresRecord?.createdAt || selectedIssue.azureRecord?.createdAt || new Date().toISOString(),
                    updatedAt: selectedIssue.postgresRecord?.updatedAt || selectedIssue.azureRecord?.updatedAt || new Date().toISOString()
                  }]
                }}
                onMerge={(primaryId, duplicateIds) => {
                  handleResolveIssue(selectedIssue.id, "merge", primaryId);
                }}
              />
            </>
          )}
        </>
      )}
    </CardContent>
  </Card>
  );
}
